bl_info = {
    "name": "Edit My Custom Tab",
    "author": "Cascade",
    "version": (1, 0),
    "blender": (2, 80, 0),
    "location": "View3D > Sidebar > My Tab",
    "description": "Adds a new button to the 'My Tab' panel.",
    "warning": "",
    "doc_url": "",
    "category": "3D View",
}

import bpy

# This function defines the UI for the new button.
# It will be 'prepended' (added to the beginning) of the existing panel's draw function.
def draw_new_button(self, context):
    layout = self.layout
    row = layout.row(align=True)
    # This operator will add a UV Sphere.
    row.operator("mesh.primitive_uv_sphere_add", text="Add Sphere")

def register():
    # We need to make sure the original panel class from 'create-tab.py' exists before trying to modify it.
    # This script should be run *after* 'create-tab.py' or if the original addon is installed.
    try:
        # The name 'VIEW3D_PT_my_custom_panel' comes from the bl_idname in the first script.
        # 'prepend' adds our new drawing function to the beginning of the panel's layout.
        bpy.types.VIEW3D_PT_my_custom_panel.prepend(draw_new_button)
    except AttributeError:
        # This will happen if the original panel's class is not registered.
        print("Original panel 'VIEW3D_PT_my_custom_panel' not found. Make sure the original script is run or the addon is enabled.")
        pass

def unregister():
    # When unregistering, we should remove the function we added to be clean.
    try:
        bpy.types.VIEW3D_PT_my_custom_panel.remove(draw_new_button)
    except AttributeError:
        pass

if __name__ == "__main__":
    # This allows the script to be run directly from Blender's Text Editor.
    register()
