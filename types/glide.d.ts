interface GlideOptions {
  type?: string
  startAt?: number
  perView?: number
  focusAt?: number | string
  gap?: number
  autoplay?: boolean | number
  hoverpause?: boolean
  keyboard?: boolean
  bound?: boolean
  swipeThreshold?: number | boolean
  dragThreshold?: number | boolean
  perTouch?: number | boolean
  touchRatio?: number
  touchAngle?: number
  animationDuration?: number
  rewind?: boolean
  rewindDuration?: number
  animationTimingFunc?: string
  direction?: string
  peek?: number | object
  breakpoints?: object
  classes?: object
  throttle?: number
}

declare class Glide {
  constructor(element: string | Element, options?: GlideOptions)
  mount(): this
  destroy(): void
  update(props?: object): void
  on(event: string, callback: Function): void
  go(pattern: string): void
  pause(): void
  play(force?: number): void
  disable(): void
  enable(): void
  isType(name: string): boolean
}

interface Window {
  Glide: typeof Glide
}
