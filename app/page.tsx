import Header from "@/components/header"
import Footer from "@/components/footer"
import GlideCarousel from "@/components/glide-carousel"
import MountainComponent from "@/components/mountain-component"
import OceanComponent from "@/components/ocean-component"
import CityComponent from "@/components/city-component"
import ForestComponent from "@/components/forest-component"
import ValleyComponent from "@/components/valley-component"
import { PricingComponent } from "@/components/pricing-component"
import { basicPricingTiers, saasPricingTiers } from "@/data/pricing-data"

export default function Page() {
  // Sample slides data with React components for all slides
  const slides = [
    {
      id: 1,
      image:
        "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80",
      title: "Mountain Landscape",
      contentComponent: MountainComponent,
      contentProps: {
        title: "Mountain Landscape",
      },
      contentType: "react",
    },
    {
      id: 2,
      image:
        "https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80",
      title: "Ocean Sunset",
      contentComponent: OceanComponent,
      contentProps: {
        title: "Ocean Sunset",
      },
      contentType: "react",
    },
    {
      id: 3,
      image:
        "https://images.unsplash.com/photo-1477959858617-67f85cf4f1df?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80",
      title: "City Skyline",
      contentComponent: CityComponent,
      contentProps: {
        title: "City Skyline",
      },
      contentType: "react",
    },
    {
      id: 4,
      image:
        "https://images.unsplash.com/photo-1447752875215-b2761acb3c5d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80",
      title: "Forest Path",
      contentComponent: ForestComponent,
      contentProps: {
        title: "Forest Path",
      },
      contentType: "react",
    },
    {
      id: 5,
      image:
        "https://images.unsplash.com/photo-1472214103451-9374bd1c798e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1000&q=80",
      title: "Green Valley",
      contentComponent: ValleyComponent,
      contentProps: {
        title: "Green Valley",
      },
      contentType: "react",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <Header />

      {/* Glide Carousel Section with custom margins */}
      <section className="relative pt-20 pb-12">
        <div className="w-full">
          <GlideCarousel
            slides={slides}
            margin={{
              top: "0rem",
              bottom: "1rem",
              left: "0rem",
              right: "0rem",
            }}
          />
        </div>
      </section>

      {/* Main content with top and bottom padding for fixed header and footer */}
      <main className="pb-24 px-6">
      </main>
      <Footer />
    </div>
  )
}
