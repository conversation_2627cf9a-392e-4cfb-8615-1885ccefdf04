"use client";

import { useState, useCallback, useEffect } from "react";
import { useTheme } from 'next-themes';
import Controls from "../../components/physics/Controls";
import KineticCanvas from "../../components/physics/KineticCanvas";
import PresetList from "../../components/physics/PresetList";
import { TextOptions, Preset } from "../../components/physics/types";
import { presets } from "../../components/physics/presets";

interface ActivePreset {
  id: number;
  name: string;
  alt?: string;
  mode: 'default' | 'character';
}

interface ReplaceTrigger {
  presetId: number;
  altFilename: string;
}

interface ModeSwapTrigger {
  presetId: number;
  newMode: 'default' | 'character';
}

export default function PhysicsText() {
  const { theme } = useTheme();
  const [canvasColor, setCanvasColor] = useState("#FFFFFF");
  const [floorColor, setFloorColor] = useState("#FFFFFF");

  useEffect(() => {
    if (theme === 'dark') {
      setCanvasColor('#000000');
      setFloorColor('#000000');
    } else {
      setCanvasColor('#FFFFFF');
      setFloorColor('#FFFFFF');
    }
  }, [theme]);

  const [inputText, setInputText] = useState("Hello Physics!");
  const [textToRender, setTextToRender] = useState<string | null>(null);
  const [dropTrigger, setDropTrigger] = useState(0);
  const [isCharacterMode, setIsCharacterMode] = useState(false);
  const [isAltMode, setIsAltMode] = useState(false);
  const [gravity, setGravity] = useState(1);
  const [friction, setFriction] = useState(0.1);
  const [restitution, setRestitution] = useState(0.8);
  const [resetTrigger, setResetTrigger] = useState(0);
  const [isPaused, setIsPaused] = useState(false);

  const [activePresets, setActivePresets] = useState<ActivePreset[]>([]);
  const [nextPresetId, setNextPresetId] = useState(1);
  const [deletePresetId, setDeletePresetId] = useState<number | null>(null);
  const [replaceTrigger, setReplaceTrigger] = useState<ReplaceTrigger | null>(null);
  const [modeSwapTrigger, setModeSwapTrigger] = useState<ModeSwapTrigger | null>(null);
  const [selectedPreset, setSelectedPreset] = useState<Preset | null>(null);
  const [frozenPresetIds, setFrozenPresetIds] = useState(new Set<number>());
  const [wallOffsetX, setWallOffsetX] = useState(0);
  const [wallOffsetY, setWallOffsetY] = useState(0);
  const [wallBorderColor, setWallBorderColor] = useState("#000000");
  const [drawingTool, setDrawingTool] = useState<'pen' | 'eraser' | 'hand'>('hand');
  const [penSize, setPenSize] = useState(5);
  const [penColor, setPenColor] = useState("#000000");
  const [isLowPoly, setIsLowPoly] = useState(false);
  const [showInvisible, setShowInvisible] = useState(false);
  const [saveDrawingTrigger, setSaveDrawingTrigger] = useState(0);
  const [drawingToLoad, setDrawingToLoad] = useState<any>(null);
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(false);

  const handleLoadDrawing = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const json = JSON.parse(e.target?.result as string);
          setDrawingToLoad(json);
        } catch (error) {
          console.error("Error parsing JSON file:", error);
          alert("Invalid drawing file.");
        }
      };
      reader.readAsText(file);
    }
  };

  const handleSetDrawingTool = (tool: 'pen' | 'eraser' | 'hand') => {
    if (tool === 'hand') {
      setDrawingTool('hand');
      return;
    }
    setDrawingTool(prevTool => (prevTool === tool ? 'hand' : tool));
  };

  const [textOptions, setTextOptions] = useState<TextOptions>({
    fontFamily: 'Arial',
    fontSize: 24,
    isBold: false,
    isItalic: false,
    letterSpacing: 0,
    color: '#000000',
    collisionColor: '#FFFFFF',
  });

  const renderText = useCallback(() => {
    if (!inputText.trim()) {
      setTextToRender(null);
      return;
    }
    setTextToRender(inputText);
  }, [inputText]);

  const dropText = () => {
    if (!textToRender) return;
    const newPreset = {
      id: nextPresetId,
      name: selectedPreset?.name || textToRender,
      alt: selectedPreset?.alt,
      mode: isCharacterMode ? 'character' as const : 'default' as const,
    };
    setActivePresets((prev) => [...prev, newPreset]);
    setDropTrigger(nextPresetId);
    setNextPresetId((prev) => prev + 1);
    setInputText("");
    setSelectedPreset(null);
  };

  const handleDropComplete = useCallback(() => {
    setTextToRender(null);
  }, []);

  const handleDeletePreset = (id: number) => {
    setDeletePresetId(id);
    setActivePresets(prev => prev.filter(p => p.id !== id));
  };

  const handleReplacePreset = (presetId: number, altFilename: string) => {
    setReplaceTrigger({ presetId, altFilename });
  };

  const handleReplaceComplete = useCallback(() => {
    setReplaceTrigger(null);
  }, []);

  const handleModeSwap = (presetId: number) => {
    const preset = activePresets.find(p => p.id === presetId);
    if (!preset) return;

    const newMode = preset.mode === 'default' ? 'character' : 'default';
    setModeSwapTrigger({ presetId, newMode });

    // Update the preset mode in state
    setActivePresets(prev => prev.map(p =>
      p.id === presetId ? { ...p, mode: newMode } : p
    ));
  };

  const handleModeSwapComplete = useCallback(() => {
    setModeSwapTrigger(null);
  }, []);

  const handleFreezePreset = (id: number) => {
    setFrozenPresetIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const handleSelectPreset = useCallback(async (presetName: string) => {
    const preset = presets.find((p) => p.name === presetName);
    if (!preset) return;

    setSelectedPreset(preset);

    if (preset.isAltMode) {
      setIsAltMode(true);
      setIsCharacterMode(false);
    } else {
      setIsAltMode(false);
    }

    try {
      const response = await fetch(`/presets/${preset.filename}`);
      if (!response.ok) throw new Error(`Failed to fetch preset file: ${preset.filename}`);
      const text = await response.text();

      setInputText(text);
      setTextToRender(text);

      setTextOptions((prev) => ({
        ...prev,
        ...preset.textOptions,
        ...(preset.collisionOptions?.color && { collisionColor: preset.collisionOptions.color }),
        ...(preset.collisionOptions?.collisionBoxSizeX && { collisionBoxSizeX: preset.collisionOptions.collisionBoxSizeX }),
        ...(preset.collisionOptions?.collisionBoxSizeY && { collisionBoxSizeY: preset.collisionOptions.collisionBoxSizeY }),
      }));

    } catch (error) {
      console.error(error);
    }
  }, []);

  const reset = () => {
    setResetTrigger((prev) => prev + 1);
    setActivePresets([]);
    setSelectedPreset(null);
  };

  const handleCollisionColorChange = (color: string) => {
    setTextOptions((prev) => ({ ...prev, collisionColor: color }));
  };

  const handleInputChange = (text: string) => {
    setInputText(text);
    setSelectedPreset(null);
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <div className="flex-grow">
        <KineticCanvas
          gravity={gravity}
          friction={friction}
          restitution={restitution}
          textToRender={textToRender}
          dropTrigger={dropTrigger}
          isCharacterMode={isCharacterMode}
          isAltMode={isAltMode}
          onReset={resetTrigger}
          isPaused={isPaused}
          canvasColor={canvasColor}
          floorColor={floorColor}
          textOptions={textOptions}
          onDropComplete={handleDropComplete}
          deletePresetId={deletePresetId}
          replaceTrigger={replaceTrigger}
          onReplaceComplete={handleReplaceComplete}
          modeSwapTrigger={modeSwapTrigger}
          onModeSwapComplete={handleModeSwapComplete}
          frozenPresetIds={frozenPresetIds}
          wallOffsetX={wallOffsetX}
          wallOffsetY={wallOffsetY}
          wallBorderColor={wallBorderColor}
          drawingTool={drawingTool}
          penSize={penSize}
          penColor={penColor}
          isLowPoly={isLowPoly}
          showInvisible={showInvisible}
          saveDrawingTrigger={saveDrawingTrigger}
          drawingToLoad={drawingToLoad}
          setWallOffsetY={setWallOffsetY}
        />
      </div>
      <div className="w-full p-4 bg-secondary shadow-md flex flex-row justify-center items-start gap-4">
        <Controls
          inputText={inputText}
          setInputText={handleInputChange}
          renderText={renderText}
          dropText={dropText}
          isTextRendered={!!textToRender}
          isCharacterMode={isCharacterMode}
          setIsCharacterMode={setIsCharacterMode}
          isAltMode={isAltMode}
          setIsAltMode={setIsAltMode}
          reset={reset}
          gravity={gravity}
          setGravity={setGravity}
          friction={friction}
          setFriction={setFriction}
          restitution={restitution}
          setRestitution={setRestitution}
          isPaused={isPaused}
          setIsPaused={setIsPaused}
          textOptions={textOptions}
          onSelectPreset={handleSelectPreset}
          canvasColor={canvasColor}
          onCanvasColorChange={setCanvasColor}
          collisionColor={textOptions.collisionColor}
          onCollisionColorChange={handleCollisionColorChange}
          floorColor={floorColor}
          onFloorColorChange={setFloorColor}
          wallOffsetX={wallOffsetX}
          setWallOffsetX={setWallOffsetX}
          wallOffsetY={wallOffsetY}
          setWallOffsetY={setWallOffsetY}
          wallBorderColor={wallBorderColor}
          onWallBorderColorChange={setWallBorderColor}
          drawingTool={drawingTool}
          setDrawingTool={handleSetDrawingTool}
          penSize={penSize}
          setPenSize={setPenSize}
          penColor={penColor}
          setPenColor={setPenColor}
          isLowPoly={isLowPoly}
          setIsLowPoly={setIsLowPoly}
          showInvisible={showInvisible}
          setShowInvisible={setShowInvisible}
          onSaveDrawing={() => setSaveDrawingTrigger(prev => prev + 1)}
          onLoadDrawing={handleLoadDrawing}
        />
        <PresetList presets={activePresets} onDelete={handleDeletePreset} onReplace={handleReplacePreset} onFreeze={handleFreezePreset} onModeSwap={handleModeSwap} frozenPresetIds={frozenPresetIds} />
      </div>
    </div>
  );
}
