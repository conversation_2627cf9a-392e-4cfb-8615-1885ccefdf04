"use client";

import { useState, useCallback, useEffect } from "react";
import { useTheme } from 'next-themes';
import Controls from "../../components/physics/Controls";
import KineticCanvas from "../../components/physics/KineticCanvas";
import PresetList from "../../components/physics/PresetList";
import { TextOptions, Preset, ActivePreset } from "../../components/physics/types";
import { presets } from "../../components/physics/presets";



interface ReplaceTrigger {
  presetId: number;
  altFilename: string;
}

interface ModeSwapTrigger {
  presetId: number;
  newMode: 'default' | 'character';
}

export default function PhysicsText() {
  const { theme } = useTheme();
  const [canvasColor, setCanvasColor] = useState("#FFFFFF");
  const [floorColor, setFloorColor] = useState("#FFFFFF");

  useEffect(() => {
    if (theme === 'dark') {
      setCanvasColor('#000000');
      setFloorColor('#000000');
    } else {
      setCanvasColor('#FFFFFF');
      setFloorColor('#FFFFFF');
    }
  }, [theme]);

  const [inputText, setInputText] = useState("Hello Physics!");
  const [textToRender, setTextToRender] = useState<string | null>(null);
  const [dropTrigger, setDropTrigger] = useState(0);
  const [directCreateTrigger, setDirectCreateTrigger] = useState<ActivePreset[] | null>(null);
  const [isCharacterMode, setIsCharacterMode] = useState(false);
  const [isAltMode, setIsAltMode] = useState(false);
  const [gravity, setGravity] = useState(1);
  const [friction, setFriction] = useState(0.1);
  const [restitution, setRestitution] = useState(0.8);
  const [resetTrigger, setResetTrigger] = useState(0);
  const [isPaused, setIsPaused] = useState(false);

  const [activePresets, setActivePresets] = useState<ActivePreset[]>([]);
  const [nextPresetId, setNextPresetId] = useState(1);
  const [deletePresetId, setDeletePresetId] = useState<number | null>(null);
  const [replaceTrigger, setReplaceTrigger] = useState<ReplaceTrigger | null>(null);
  const [modeSwapTrigger, setModeSwapTrigger] = useState<ModeSwapTrigger | null>(null);
  const [recoverTrigger, setRecoverTrigger] = useState<number | null>(null);
  const [selectedPreset, setSelectedPreset] = useState<Preset | null>(null);
  const [frozenPresetIds, setFrozenPresetIds] = useState(new Set<number>());
  const [wallOffsetX, setWallOffsetX] = useState(0);
  const [wallOffsetY, setWallOffsetY] = useState(0);
  const [wallBorderColor, setWallBorderColor] = useState("#000000");
  const [drawingTool, setDrawingTool] = useState<'pen' | 'eraser' | 'hand'>('hand');
  const [penSize, setPenSize] = useState(5);
  const [penColor, setPenColor] = useState("#000000");
  const [isLowPoly, setIsLowPoly] = useState(false);
  const [showInvisible, setShowInvisible] = useState(false);
  const [saveDrawingTrigger, setSaveDrawingTrigger] = useState(0);
  const [drawingToLoad, setDrawingToLoad] = useState<any>(null);
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(false);

  // Performance optimization toggles
  const [performanceOptimizations, setPerformanceOptimizations] = useState({
    suspendStaticBodies: false,
    disableCollisionDetection: false,
    reduceRenderFrequency: false,
    freezeDistantBodies: false,
    batchPhysicsUpdates: false,
  });

  // Performance thresholds
  const PERFORMANCE_THRESHOLD = 20;
  const AUTO_SUGGEST_THRESHOLD = 15;
  const MAX_OPTIMIZATION_THRESHOLD = 50; // Disable optimizations when too many bodies

  const handleLoadDrawing = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const json = JSON.parse(e.target?.result as string);
          setDrawingToLoad(json);
        } catch (error) {
          console.error("Error parsing JSON file:", error);
          alert("Invalid drawing file.");
        }
      };
      reader.readAsText(file);
    }
  };

  const handleSetDrawingTool = (tool: 'pen' | 'eraser' | 'hand') => {
    if (tool === 'hand') {
      setDrawingTool('hand');
      return;
    }
    setDrawingTool(prevTool => (prevTool === tool ? 'hand' : tool));
  };

  const [textOptions, setTextOptions] = useState<TextOptions>({
    fontFamily: 'Arial',
    fontSize: 24,
    isBold: false,
    isItalic: false,
    letterSpacing: 0,
    color: '#000000',
    collisionColor: '#FFFFFF',
  });

  const renderText = useCallback(() => {
    if (!inputText.trim()) {
      setTextToRender(null);
      return;
    }
    setTextToRender(inputText);
  }, [inputText]);

  const dropText = () => {
    if (!textToRender) return;
    const newPreset: ActivePreset = {
      id: nextPresetId,
      name: selectedPreset?.name || textToRender,
      text: textToRender,
      options: textOptions,
      mode: isCharacterMode ? 'character' as const : 'default' as const,
    };
    setActivePresets((prev) => [...prev, newPreset]);
    setDropTrigger(nextPresetId);
    setNextPresetId((prev) => prev + 1);
    setInputText("");
    setSelectedPreset(null);
  };

  const handleDropComplete = useCallback(() => {
    setTextToRender(null);
  }, []);

  const handleDeletePreset = (id: number) => {
    setDeletePresetId(id);
    setActivePresets(prev => prev.filter(p => p.id !== id));
  };

  const handleReplacePreset = (presetId: number, altFilename: string) => {
    setReplaceTrigger({ presetId, altFilename });
  };

  const handleReplaceComplete = useCallback(() => {
    setReplaceTrigger(null);
  }, []);

  const handleModeSwap = (presetId: number) => {
    const preset = activePresets.find(p => p.id === presetId);
    if (!preset) return;

    const newMode = preset.mode === 'default' ? 'character' : 'default';
    setModeSwapTrigger({ presetId, newMode });

    // Update the preset mode in state
    setActivePresets(prev => prev.map(p =>
      p.id === presetId ? { ...p, mode: newMode } : p
    ));
  };

  const handleModeSwapComplete = useCallback(() => {
    setModeSwapTrigger(null);
  }, []);

  const handleRecover = useCallback((presetId: number) => {
    // Update the preset mode to default
    setActivePresets(prev => prev.map(preset =>
      preset.id === presetId
        ? { ...preset, mode: 'default' as const }
        : preset
    ));

    // Trigger the recovery animation
    setRecoverTrigger(presetId);
  }, []);

  const handleRecoverComplete = useCallback(() => {
    setRecoverTrigger(null);
  }, []);

  const handleDeleteAllPresets = useCallback(() => {
    if (activePresets.length === 0) return;

    // Confirm deletion if there are many presets
    if (activePresets.length > 3) {
      const confirmed = window.confirm(
        `Are you sure you want to delete all ${activePresets.length} active presets? This will free up memory but cannot be undone.`
      );
      if (!confirmed) return;
    }

    // Clear all active presets and trigger cleanup
    setActivePresets([]);
    setFrozenPresetIds(new Set());

    // Trigger a reset to clean up physics bodies
    setResetTrigger(prev => prev + 1);
  }, [activePresets.length]);

  // Add 10 random elements at once
  const handleAdd10Random = useCallback(() => {
    const randomTexts = [
      "Hello World!", "Physics!", "Random Text", "Bouncing", "Gravity",
      "Matter.js", "React", "TypeScript", "Performance", "Optimization",
      "Collision", "Dynamic", "Static", "Velocity", "Force",
      "Simulation", "Canvas", "Render", "Engine", "Bodies"
    ];

    const randomColors = [
      "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
      "#DDA0DD", "#98D8C8", "#F7DC6F", "#BB8FCE", "#85C1E9"
    ];

    const newPresets: ActivePreset[] = [];
    for (let i = 0; i < 10; i++) {
      const randomText = randomTexts[Math.floor(Math.random() * randomTexts.length)];
      const randomColor = randomColors[Math.floor(Math.random() * randomColors.length)];
      const randomMode = Math.random() > 0.5 ? 'character' : 'default';

      const newPreset: ActivePreset = {
        id: nextPresetId + i,
        name: `Random ${i + 1}`,
        text: randomText,
        options: {
          ...textOptions,
          color: randomColor,
          collisionColor: randomColor,
          fontSize: Math.floor(Math.random() * 20) + 16, // 16-36px
        },
        mode: randomMode as 'default' | 'character',
      };
      newPresets.push(newPreset);
    }

    setActivePresets(prev => [...prev, ...newPresets]);
    setNextPresetId(prev => prev + 10);

    // Set a special trigger to create bodies directly (bypassing preview system)
    setDirectCreateTrigger(newPresets);
  }, [nextPresetId, textOptions]);

  // Performance optimization handlers
  const handlePerformanceToggle = useCallback((optimization: keyof typeof performanceOptimizations) => {
    setPerformanceOptimizations(prev => ({
      ...prev,
      [optimization]: !prev[optimization]
    }));
  }, []);

  // Auto-suggest performance optimizations when body count is high, but disable when too many
  const shouldSuggestOptimizations = activePresets.length >= AUTO_SUGGEST_THRESHOLD && activePresets.length < MAX_OPTIMIZATION_THRESHOLD;
  const isHighBodyCount = activePresets.length >= PERFORMANCE_THRESHOLD;
  const isTooManyBodies = activePresets.length >= MAX_OPTIMIZATION_THRESHOLD;

  const handleFreezePreset = (id: number) => {
    setFrozenPresetIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const handleSelectPreset = useCallback(async (presetName: string) => {
    const preset = presets.find((p) => p.name === presetName);
    if (!preset) return;

    setSelectedPreset(preset);

    if (preset.isAltMode) {
      setIsAltMode(true);
      setIsCharacterMode(false);
    } else {
      setIsAltMode(false);
    }

    try {
      const response = await fetch(`/presets/${preset.filename}`);
      if (!response.ok) throw new Error(`Failed to fetch preset file: ${preset.filename}`);
      const text = await response.text();

      setInputText(text);
      setTextToRender(text);

      setTextOptions((prev) => ({
        ...prev,
        ...preset.textOptions,
        ...(preset.collisionOptions?.color && { collisionColor: preset.collisionOptions.color }),
        ...(preset.collisionOptions?.collisionBoxSizeX && { collisionBoxSizeX: preset.collisionOptions.collisionBoxSizeX }),
        ...(preset.collisionOptions?.collisionBoxSizeY && { collisionBoxSizeY: preset.collisionOptions.collisionBoxSizeY }),
      }));

    } catch (error) {
      console.error(error);
    }
  }, []);

  const reset = () => {
    setResetTrigger((prev) => prev + 1);
    setActivePresets([]);
    setSelectedPreset(null);
  };

  const handleCollisionColorChange = (color: string) => {
    setTextOptions((prev) => ({ ...prev, collisionColor: color }));
  };

  const handleInputChange = (text: string) => {
    setInputText(text);
    setSelectedPreset(null);
  };

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <div className="flex-grow">
        <KineticCanvas
          gravity={gravity}
          friction={friction}
          restitution={restitution}
          textToRender={textToRender}
          dropTrigger={dropTrigger}
          directCreateTrigger={directCreateTrigger}
          onDirectCreateComplete={() => setDirectCreateTrigger(null)}
          isCharacterMode={isCharacterMode}
          isAltMode={isAltMode}
          onReset={resetTrigger}
          isPaused={isPaused}
          canvasColor={canvasColor}
          floorColor={floorColor}
          textOptions={textOptions}
          onDropComplete={handleDropComplete}
          deletePresetId={deletePresetId}
          replaceTrigger={replaceTrigger}
          onReplaceComplete={handleReplaceComplete}
          modeSwapTrigger={modeSwapTrigger}
          onModeSwapComplete={handleModeSwapComplete}
          recoverTrigger={recoverTrigger}
          onRecoverComplete={handleRecoverComplete}
          frozenPresetIds={frozenPresetIds}
          wallOffsetX={wallOffsetX}
          wallOffsetY={wallOffsetY}
          wallBorderColor={wallBorderColor}
          drawingTool={drawingTool}
          penSize={penSize}
          penColor={penColor}
          isLowPoly={isLowPoly}
          showInvisible={showInvisible}
          saveDrawingTrigger={saveDrawingTrigger}
          drawingToLoad={drawingToLoad}
          setWallOffsetY={setWallOffsetY}
          performanceOptimizations={performanceOptimizations}
        />
      </div>
      <div className="w-full p-4 bg-secondary shadow-md flex flex-row justify-center items-start gap-4">
        <Controls
          inputText={inputText}
          setInputText={handleInputChange}
          renderText={renderText}
          dropText={dropText}
          isTextRendered={!!textToRender}
          isCharacterMode={isCharacterMode}
          setIsCharacterMode={setIsCharacterMode}
          isAltMode={isAltMode}
          setIsAltMode={setIsAltMode}
          reset={reset}
          gravity={gravity}
          setGravity={setGravity}
          friction={friction}
          setFriction={setFriction}
          restitution={restitution}
          setRestitution={setRestitution}
          isPaused={isPaused}
          setIsPaused={setIsPaused}
          textOptions={textOptions}
          onSelectPreset={handleSelectPreset}
          canvasColor={canvasColor}
          onCanvasColorChange={setCanvasColor}
          collisionColor={textOptions.collisionColor}
          onCollisionColorChange={handleCollisionColorChange}
          floorColor={floorColor}
          onFloorColorChange={setFloorColor}
          wallOffsetX={wallOffsetX}
          setWallOffsetX={setWallOffsetX}
          wallOffsetY={wallOffsetY}
          setWallOffsetY={setWallOffsetY}
          wallBorderColor={wallBorderColor}
          onWallBorderColorChange={setWallBorderColor}
          drawingTool={drawingTool}
          setDrawingTool={handleSetDrawingTool}
          penSize={penSize}
          setPenSize={setPenSize}
          penColor={penColor}
          setPenColor={setPenColor}
          isLowPoly={isLowPoly}
          setIsLowPoly={setIsLowPoly}
          showInvisible={showInvisible}
          setShowInvisible={setShowInvisible}
          onSaveDrawing={() => setSaveDrawingTrigger(prev => prev + 1)}
          onLoadDrawing={handleLoadDrawing}
          onDeleteAllPresets={handleDeleteAllPresets}
          onAdd10Random={handleAdd10Random}
          activePresetsCount={activePresets.length}
          performanceOptimizations={performanceOptimizations}
          onPerformanceToggle={handlePerformanceToggle}
          shouldSuggestOptimizations={shouldSuggestOptimizations}
          isHighBodyCount={isHighBodyCount}
          isTooManyBodies={isTooManyBodies}
        />
        <PresetList presets={activePresets} onDelete={handleDeletePreset} onReplace={handleReplacePreset} onFreeze={handleFreezePreset} onModeSwap={handleModeSwap} onRecover={handleRecover} frozenPresetIds={frozenPresetIds} />
      </div>
    </div>
  );
}
