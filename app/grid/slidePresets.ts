// The main interface used by components, includes the generated ID
export interface SlidePreset {
  id: string; // Unique ID for key prop, will be auto-generated
  title: string;
  slideContent: string;
  iconContent: string; // URL for the icon/image
  carouselTarget: number; // Specifies which carousel this preset belongs to (e.g., 1, 2, 3)
  category: string; // Category of the preset
}

// Helper type for defining preset data without manually setting an ID
interface SlidePresetInputData extends Omit<SlidePreset, 'id'> {}

// --- Procedural Preset Generation ---

const proceduralPhrases = [
  '{name} just installed your addon from Gumroad.',
  '{name} passed DRM.',
  '{name} has not activated the addon in 2 days.'
];

const proceduralIcon = 'https://static-00.iconduck.com/assets.00/user-icon-2048x2048-ihoxz4vq.png'; // Generic user icon

/**
 * Creates a new slide preset procedurally based on a given name.
 * @param name The name to insert into the slide content (e.g., a user's name).
 * @returns A new slide preset object conforming to the SlidePresetInputData interface.
 */
export function createProceduralPreset(name: string): SlidePresetInputData {
  const phraseTemplate = proceduralPhrases[Math.floor(Math.random() * proceduralPhrases.length)];
  const slideContent = phraseTemplate.replace('{name}', name);
  const carouselTarget = Math.floor(Math.random() * 3) + 1; // Randomly assign to carousel 1, 2, or 3

  return {
    title: name,
    slideContent,
    iconContent: proceduralIcon,
    carouselTarget,
    category: 'notification'
  };
}

// Raw preset data - define your presets here without the 'id' field
const rawPresetData: SlidePresetInputData[] = [
  {
    title: 'GitLab CI',
    slideContent: 'Automate the build, test, and deployment of your code. GitLab CI/CD helps you catch bugs early and release more often.',
    iconContent: 'https://cdn.simpleicons.org/gitlab/FC6D26',
    carouselTarget: 1,
    category: 'workflow'
  },
  {
    title: 'GitHub Actions',
    slideContent: 'Automate your build, test, and deployment pipeline. Create workflows that build and test every pull request, or deploy merged pull requests to production.',
    iconContent: 'https://cdn.simpleicons.org/githubactions/FFFFFF',
    carouselTarget: 1,
    category: 'workflow'
  },
  {
    title: 'Local Folder',
    slideContent: 'Your local development environment is where all the magic happens. A professional setup lets you be as efficient as possible.',
    iconContent: 'https://uxwing.com/wp-content/themes/uxwing/download/file-and-folder/folder-icon.svg',
    carouselTarget: 1,
    category: 'workflow'
  },
  {
    title: 'Zip File',
    slideContent: 'An archive file format that supports lossless data compression. A ZIP file may contain one or more files or directories.',
    iconContent: 'https://uxwing.com/wp-content/themes/uxwing/download/file-and-folder/zip-file-icon.svg',
    carouselTarget: 1,
    category: 'workflow'
  },
  {
    title: 'Advanced Encrypt',
    slideContent: 'The process of converting readable information into an unreadable format to protect it from unauthorized access.',
    iconContent: 'https://uxwing.com/wp-content/themes/uxwing/download/internet-network-technology/encryption-icon.svg',
    carouselTarget: 2,
    category: 'workflow'
  },
  {
    title: 'Fast Encrypt',
    slideContent: 'Significantly faster than asymmetric encryption, symmetric algorithms require less computational power and don\'t dampen internet speed.',
    iconContent: 'https://uxwing.com/wp-content/themes/uxwing/download/internet-network-technology/encryption-icon.svg',
    carouselTarget: 2,
    category: 'workflow'
  },
  {
    title: 'Silent Encrypt',
    slideContent: 'Seamlessly encrypts your data in the background, providing robust security without interrupting your workflow.',
    iconContent: 'https://uxwing.com/wp-content/themes/uxwing/download/internet-network-technology/encryption-icon.svg',
    carouselTarget: 2,
    category: 'workflow'
  },
  {
    title: 'Gumroad',
    slideContent: 'An e-commerce platform that allows creators to sell products directly to their audience.',
    iconContent: 'https://cdn.simpleicons.org/gumroad/FFFFFF',
    carouselTarget: 3,
    category: 'workflow'
  },
  {
    title: 'Superhive',
    slideContent: 'A unique market for creators that love Blender, empowering artists and developers to grow professionally.',
    iconContent: 'https://cdn.simpleicons.org/hive/FFFFFF',
    carouselTarget: 3,
    category: 'workflow'
  },
  {
    title: 'SurrealShop',
    slideContent: 'The live entertainment productivity tool, built for hospitality. Schedule and book across venues, securely manage your entertainer roster and automate invoicing.',
    iconContent: 'https://cdn.simpleicons.org/surrealdb/FFFFFF',
    carouselTarget: 3,
    category: 'workflow'
  },
  {
    title: 'Itch.io',
    slideContent: 'An open marketplace for independent digital creators with a focus on independent video games. It’s a platform that enables anyone to sell the content they\'ve created.',
    iconContent: 'https://static.itch.io/images/itchio-logo-black.svg',
    carouselTarget: 3,
    category: 'workflow'
  },
  {
    title: 'Blenderkit',
    slideContent: 'An online database of materials, brushes and 3D models which you can search, download, upload and rate directly from the add-on.',
    iconContent: 'https://uxwing.com/wp-content/themes/uxwing/download/brands-and-social-media/blender-icon.svg',
    carouselTarget: 3,
    category: 'workflow'
  }
];

// Generate the final defaultSlidePresets array with auto-generated IDs
export const defaultSlidePresets: SlidePreset[] = rawPresetData.map((preset, index) => ({
  ...preset,
  id: `preset-${index + 1}` // Generates IDs like 'preset-1', 'preset-2', etc.
}));
