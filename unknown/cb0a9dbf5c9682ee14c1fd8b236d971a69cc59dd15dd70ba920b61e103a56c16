'use client'
import React, { useState } from 'react'
import EmblaCarousel from './EmblaCarousel'
import { EmblaOptionsType } from 'embla-carousel'

import './base.css'
import './embla.css'

const OPTIONS: EmblaOptionsType = { loop: true }
const OPTIONS2: EmblaOptionsType = { axis: 'y', loop: true }

import { defaultSlidePresets, SlidePreset, createProceduralPreset } from './slidePresets';

const App: React.FC = () => {
  const [slidesData, setSlidesData] = useState<SlidePreset[]>(defaultSlidePresets);
  const [autoplayDelay, setAutoplayDelay] = useState<number>(2000);
  const [nameInput, setNameInput] = useState('');

  const handleSlideContentChange = (index: number, newContent: string) => {
    const newSlidesData = slidesData.map((slide, i) => {
      if (i === index) {
        return { ...slide, slideContent: newContent };
      }
      return slide;
    });
    setSlidesData(newSlidesData);
  };

  const handleAddProceduralPreset = () => {
    if (!nameInput.trim()) return; // Don't add if name is empty
    const newPresetData = createProceduralPreset(nameInput);
    const newPresetWithId: SlidePreset = {
      ...newPresetData,
      id: `preset-${slidesData.length + 1}` // Generate a unique ID
    };
    setSlidesData(prevSlides => [...prevSlides, newPresetWithId]);
    setNameInput(''); // Clear the input field
  };

  return (
    <>
      <div style={{ marginTop: '20px', marginBottom: '20px', padding: '15px', border: '1px solid #ccc', borderRadius: '8px', backgroundColor: '#f9f9f9' }}>
        <label htmlFor="autoplayDelayInput" style={{ marginRight: '10px', fontWeight: 'bold' }}>Autoplay Delay (ms):</label>
        <input 
          type="number" 
          id="autoplayDelayInput" 
          value={autoplayDelay}
          onChange={(e) => setAutoplayDelay(parseInt(e.target.value, 10) || 2000)} 
          style={{ padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
        />
      </div>
      <div style={{ marginTop: '20px', marginBottom: '20px', padding: '15px', border: '1px solid #ccc', borderRadius: '8px', backgroundColor: '#f9f9f9' }}>
        <label htmlFor="nameInput" style={{ marginRight: '10px', fontWeight: 'bold' }}>Name:</label>
        <input
          id="nameInput"
          type="text"
          value={nameInput}
          onChange={(e) => setNameInput(e.target.value)}
          placeholder="Enter a name"
          style={{ padding: '5px', border: '1px solid #ddd', borderRadius: '4px', marginRight: '10px' }}
        />
        <button onClick={handleAddProceduralPreset} style={{ padding: '5px 10px', border: '1px solid #ccc', borderRadius: '4px', cursor: 'pointer' }}>
          Generate Notification
        </button>
      </div>
      <div>
        <EmblaCarousel slides={slidesData.filter(slide => slide.carouselTarget === 1)} options={OPTIONS} autoplayDelay={autoplayDelay} />
      </div>
      <div>
        <EmblaCarousel slides={slidesData.filter(slide => slide.carouselTarget === 2)} options={OPTIONS} autoplayDelay={autoplayDelay} />
      </div>
      <div>
        <EmblaCarousel slides={slidesData.filter(slide => slide.carouselTarget === 3)} options={OPTIONS} autoplayDelay={autoplayDelay} />
      </div>
      <div>
        <EmblaCarousel slides={slidesData.filter(slide => slide.carouselTarget === 4)} options={OPTIONS2} autoplayDelay={autoplayDelay} className="embla--vertical" />
      </div>
    </>
  )
}

export default App
