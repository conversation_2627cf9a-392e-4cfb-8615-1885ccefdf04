SURREAL PAGE
why samy
what is samy
projects
what is open source
work at surreal

SURREALSHOP
categories

p1 ok
p2 scroll activates drop. letter roll.
p3 text fall in trap
p4 text recover from fall

a script that turns sketch into rig animation

preferences: customize the app as you wish                                                     GEAR-
real-time: recieve notification in your dashboard for every purchase          NOTIFACITON-
workflow orchestration: simple drag and drop github action builder          DRAG AND DROPPING- NEEDS ICONS AND TEXT
block based editor: customize the app, make it fit                                            EXAMPLE APP - NEEDS ADDON
install and ship: install steel once, never worry again                                     STEEL LOGO????
unlimited: no limit of installs                                                                                 ADDING NUMBERS
dashboard: manage installs and checks                                                             NTOIFICATIONS              FIX THIS

                        part of your stack: integrate services you like, cython, github actions, gitlab, gumroad, etc.         A NET OF CIRCLES


hmac encryption: several encryption, tested and perfect ed                                                                  GLB LOCK -

ASCII TRANSITION IN FUTURE UPDATE
how it wokrs (you have text that turns into encoded)                                                                        CARD WITH LINE IN MIDDLE
1 install, using github, zip files, or any git                                                                                                    ASCII HAND WITH DISKETTE
2 encrypt per user, in sha1                                                                                                                   DISKETTE TURNING INTO ASCII
3 check and decrypt, users will prove ownership by logging in                                                              FAKE DISKETE BETWEEN REAL ONES


example appand ship: install steel once, never worry again

ffmpeg -i i.gif -movflags faststart -pix_fmt yuv420p -vf "scale=trunc(iw/2)*2:trunc(ih/2)*2" -vf negate video.mp4


DASHBOARD LIST
Top, wallet
AUDIT LOG IS ALMOST DONE
WEBHOOKS ARE SUPPORTED
API

MAIL SUSPEND UNTIL PORTAFOLIO IS DONE

TODO DARK THEME

SETUP                                                                                     DONE
GITHUB ACTIONS GENERATOR                                         TODO
page0 choose plan
page1 choose provider and repo
page2 choose store
page3 loading screen
page4 success

WALLET                                                                                    DONE
EXPLAIN                                                                                 TODO
separrate from surrealshop and real credits
option to use only real credits
change the plan
add money

CUSTOMIZE                                                                          DONE-V2
extra tab version
pop up version
edit current tab version
change icon
remove watermark

SETTINGS                                                                              DONE
IMPLEMENT  ACCOUNT                                                      TODOS

API DOCS
tutorials and quickstarts
api reference
security

get started
-setup account
-setup project
-add money

references overview:  Steel SDK
-installation
-setup dev
-writing code

Security
-advanced encryption
-fast encryption
-silent encryption

SAAS
ngrok http --url=probable-major-iguana.ngrok-free.app 4002

SHOP ADMIN

SHOP FRONT
