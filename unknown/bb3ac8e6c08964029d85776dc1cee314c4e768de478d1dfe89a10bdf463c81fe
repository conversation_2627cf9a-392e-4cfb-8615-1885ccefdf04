import * as THREE from 'three';

export interface ExtrudedImageOptions {
  thickness: number;
  sizeX: number;
  sizeY: number;
  alphaThreshold: number;
  materialParams?: {
    color?: THREE.ColorRepresentation;
  };
  customMaterial?: THREE.Material; // Allow more flexible material types
  legacy?: boolean;
  animationSpeed: number; // For GIFs, frames per second
  pixelationFactor: number; // 1 for original, >1 for less detail (e.g., 2 means 2x2 blocks become one pixel)
  useAsciiFilter?: boolean;
  asciiCharacters?: string;
  asciiFontSize?: number;
  asciiCellSize?: number;
  asciiColor?: string;
  asciiBackgroundColor?: string;
  asciiInvert?: boolean;
  imageUrl?: string; // To pass image URL
  imageData?: ImageData; // To pass raw image data
  isGif?: boolean;
  gifFrames?: ImageData[];
  blackThreshold?: number; // Max R,G,B value to be considered 'black' (0-255), for sensitivity
  brightness?: number; // Texture brightness multiplier (e.g., 0.0 to 2.0, 1.0 is normal)
  glbPingPongLoop?: boolean; // For GLB animation: play forward, then reverse, then loop
  // GLB Lighting Options
  glbAmbientLightIntensity?: number;
  glbDirectionalLightIntensity?: number;
  glbDirectionalLightColor?: string;
  glbDirectionalLightPosition?: [number, number, number];
  // Scene Post-Processing Options
  sceneBrightness?: number; // Range -1 to 1, default 0
  sceneContrast?: number;   // Range -1 to 1, default 0
  // GLB Solid Color Options
  glbUseSolidColor?: boolean;
  glbSolidColor?: string;
}

export interface ExtrusionPreset {
  name: string; // Name for the button
  imageSrc?: string; // URL for the image, optional if glbSrc is provided
  glbSrc?: string; // URL for the GLB model, optional if imageSrc is provided
  brightness?: number; // Make optional as it's image-specific
  sizeX: number;
  pixelationFactor?: number; // Make optional as it's image-specific
  sideColor?: string; // Optional side face color for the preset
  thickness?: number; // Optional thickness for the preset
  asciiColor?: string; // Optional ASCII text color
  asciiBackgroundColor?: string; // Optional ASCII background color
  cameraPosition?: [number, number, number]; // Optional initial camera position [x, y, z]
  fov?: number; // Optional camera Field of View
  // GLB Lighting Options for Presets
  glbAmbientLightIntensity?: number;
  glbDirectionalLightIntensity?: number;
  glbDirectionalLightColor?: string;
  glbDirectionalLightPosition?: [number, number, number];
  // Scene Post-Processing Options for Presets
  sceneBrightness?: number;
  sceneContrast?: number;
  // GLB Solid Color Options for Presets
  glbUseSolidColor?: boolean;
  glbSolidColor?: string;
  // Add other options here if they should be part of the preset
}
