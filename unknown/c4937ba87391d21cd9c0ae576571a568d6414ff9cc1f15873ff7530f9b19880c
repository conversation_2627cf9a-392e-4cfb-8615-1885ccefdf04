"use client"

import React from "react"

import { useState, useEffect, useMemo, useRef } from "react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import {
  Activity,
  AlertCircle,
  Archive,
  ArrowRight,
  Bell,
  Bookmark,
  Calendar,
  Check,
  Clock,
  Cloud,
  Code,
  Cog,
  Compass,
  Database,
  Download,
  Edit,
  Eye,
  File,
  FileText,
  Flag,
  Folder,
  Heart,
  Home,
  ImageIcon,
  Info,
  Link,
  Lock,
  Mail,
  Map,
  MessageCircle,
  Moon,
  Music,
  Package,
  Phone,
  PieChart,
  Play,
  Plus,
  Search,
  Send,
  Settings,
  Share,
  ShoppingCart,
  Star,
  Sun,
  Tag,
  Terminal,
  ThumbsUp,
  Trash,
  User,
  Video,
  Zap,
  X,
  Palette,
  Grid,
  Type,
  RotateCcw,
  MoveHorizontal,
  Maximize,
  Edit3,
  Image as ImageIconLucide, // Renamed to avoid conflict if 'Image' is used elsewhere
} from "lucide-react"

interface SquareData {
  id: number
  row: number
  col: number
  spanX: number
  spanY: number
  isVisible: boolean
  isMerged: boolean
  icon: React.ComponentType<any>
  color: string
  showIcon: boolean
  title?: string
  content?: string
  imageUrl?: string // Added for remote image
}

interface SquarePosition {
  row: number
  col: number
}

interface GridLayoutState {
  columns: number
  rows: number
}

interface ResponsiveSquareGridProps {
  className?: string
  initialTotalSquares?: number
  defaultColorScheme?: "blue" | "gray" | "green" | "purple" | "amber"
}

export default function ResponsiveSquareGrid({
  className,
  initialTotalSquares = 150,
  defaultColorScheme = "blue",
}: ResponsiveSquareGridProps) {
  const [totalSquares, setTotalSquares] = useState(initialTotalSquares)
  const [gridLayout, setGridLayout] = useState<GridLayoutState>({ columns: 8, rows: 12 })
  const [selectedSquare, setSelectedSquare] = useState<number | null>(null)
  const [focusedSquare, setFocusedSquare] = useState<number | null>(null)
  const [animatingSquare, setAnimatingSquare] = useState<number | null>(null)
  const [isAnimating, setIsAnimating] = useState(false)
  const [editMode, setEditMode] = useState(false)
  const [customSizes, setCustomSizes] = useState<Record<number, { spanX: number; spanY: number }>>({})
  const [squareColors, setSquareColors] = useState<Record<number, string>>({})
  const [tempSpanX, setTempSpanX] = useState(1)
  const [tempSpanY, setTempSpanY] = useState(1)
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [squareIconVisibility, setSquareIconVisibility] = useState<Record<number, boolean>>({})
  const [squareTitles, setSquareTitles] = useState<Record<number, string>>({})
  const [squareContents, setSquareContents] = useState<Record<number, string>>({})
  const [iconSize, setIconSize] = useState(24) // Default icon size
  const [showTextEditor, setShowTextEditor] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [draggedSquare, setDraggedSquare] = useState<number | null>(null)
  const [dropTarget, setDropTarget] = useState<number | null>(null)
  const [customPositions, setCustomPositions] = useState<Record<number, SquarePosition>>({})
  const [rearrangeMode, setRearrangeMode] = useState(false)
  const [iconBackgroundColors, setIconBackgroundColors] = useState<Record<number, string>>({})
  const [showIconBgColorPicker, setShowIconBgColorPicker] = useState(false)
  const [squareSize, setSquareSize] = useState(48) // Default 3rem (48px)
  const [squareImageUrls, setSquareImageUrls] = useState<Record<number, string | undefined>>({})
  const [tempImageUrl, setTempImageUrl] = useState("");

  // Refs for animation
  const gridContainerRef = useRef<HTMLDivElement>(null)
  const squareRefs = useRef<Record<number, HTMLDivElement | null>>({})
  const cloneRef = useRef<HTMLDivElement | null>(null)

  // Available icons from Lucide
  const icons = [
    Activity,
    AlertCircle,
    ImageIconLucide, // Use the aliased Lucide icon
    Archive,
    ArrowRight,
    Bell,
    Bookmark,
    Calendar,
    Check,
    Clock,
    Cloud,
    Code,
    Cog,
    Compass,
    Database,
    Download,
    Edit,
    Eye,
    File,
    FileText,
    Flag,
    Folder,
    Heart,
    Home,
    ImageIcon,
    Info,
    Link,
    Lock,
    Mail,
    Map,
    MessageCircle,
    Moon,
    Music,
    Package,
    Phone,
    PieChart,
    Play,
    Plus,
    Search,
    Send,
    Settings,
    Share,
    ShoppingCart,
    Star,
    Sun,
    Tag,
    Terminal,
    ThumbsUp,
    Trash,
    User,
    Video,
    Zap,
  ]

  // Color options
  const colorOptions = [
    { name: "blue", square: "bg-blue-50 border-blue-200", iconBg: "bg-blue-100", iconColor: "text-blue-600" },
    { name: "gray", square: "bg-gray-50 border-gray-200", iconBg: "bg-gray-100", iconColor: "text-gray-600" },
    { name: "green", square: "bg-green-50 border-green-200", iconBg: "bg-green-100", iconColor: "text-green-600" },
    { name: "purple", square: "bg-purple-50 border-purple-200", iconBg: "bg-purple-100", iconColor: "text-purple-600" },
    { name: "amber", square: "bg-amber-50 border-amber-200", iconBg: "bg-amber-100", iconColor: "text-amber-600" },
    { name: "red", square: "bg-red-50 border-red-200", iconBg: "bg-red-100", iconColor: "text-red-600" },
    { name: "pink", square: "bg-pink-50 border-pink-200", iconBg: "bg-pink-100", iconColor: "text-pink-600" },
    { name: "indigo", square: "bg-indigo-50 border-indigo-200", iconBg: "bg-indigo-100", iconColor: "text-indigo-600" },
    { name: "cyan", square: "bg-cyan-50 border-cyan-200", iconBg: "bg-cyan-100", iconColor: "text-cyan-600" },
    { name: "orange", square: "bg-orange-50 border-orange-200", iconBg: "bg-orange-100", iconColor: "text-orange-600" },
  ]

  const iconBgColorOptions = [
    { name: "blue", iconBg: "bg-blue-100" },
    { name: "gray", iconBg: "bg-gray-100" },
    { name: "green", iconBg: "bg-green-100" },
    { name: "purple", iconBg: "bg-purple-100" },
    { name: "amber", iconBg: "bg-amber-100" },
    { name: "red", iconBg: "bg-red-100" },
    { name: "pink", iconBg: "bg-pink-100" },
    { name: "indigo", iconBg: "bg-indigo-100" },
    { name: "cyan", iconBg: "bg-cyan-100" },
    { name: "orange", iconBg: "bg-orange-100" },
    { name: "white", iconBg: "bg-white" },
    { name: "black", iconBg: "bg-black" },
  ]

  // Calculate optimal grid layout based on square size and viewport
  useEffect(() => {
    const calculateOptimalLayout = () => {
      const width = window.innerWidth
      const height = window.innerHeight

      const availableWidth = Math.min(width * 0.9, 1200)
      const availableHeight = height * 0.7

      // Calculate gap based on square size (proportional)
      const gap = Math.max(4, Math.min(12, squareSize * 0.2))

      // Calculate how many squares can fit in each dimension
      const maxColumns = Math.floor((availableWidth + gap) / (squareSize + gap))
      const maxRows = Math.floor((availableHeight + gap) / (squareSize + gap))

      // Ensure we have at least enough space for all squares
      const minColumns = Math.max(4, Math.ceil(Math.sqrt(totalSquares * 0.8)))
      const minRows = Math.max(4, Math.ceil(totalSquares / minColumns))

      // Choose the layout that fits best within constraints
      const columns = Math.max(minColumns, Math.min(maxColumns, 24))
      const rows = Math.max(minRows, Math.ceil(totalSquares / columns))

      setGridLayout({ columns, rows })
    }

    calculateOptimalLayout()
    window.addEventListener("resize", calculateOptimalLayout)

    return () => {
      window.removeEventListener("resize", calculateOptimalLayout)
    }
  }, [squareSize, totalSquares])

  // Calculate the actual number of rows needed for the current totalSquares
  const actualRows = useMemo(() => {
    return Math.max(gridLayout.rows, Math.ceil(totalSquares / gridLayout.columns))
  }, [totalSquares, gridLayout.columns, gridLayout.rows])

  // Generate squares with their positions and custom sizes
  const squares = useMemo(() => {
    const squareArray: SquareData[] = []
    const occupiedCells = new Set<string>()

    // First pass: Create a map of all squares with their default or custom positions
    const squarePositions: Array<{
      id: number
      row: number
      col: number
      spanX: number
      spanY: number
    }> = []

    for (let index = 0; index < totalSquares; index++) {
      // Get custom position if it exists, otherwise calculate default position
      const customPosition = customPositions[index]
      const defaultRow = Math.floor(index / gridLayout.columns)
      const defaultCol = index % gridLayout.columns

      const row = customPosition ? customPosition.row : defaultRow
      const col = customPosition ? customPosition.col : defaultCol

      const customSize = customSizes[index]
      const spanX = customSize?.spanX || 1
      const spanY = customSize?.spanY || 1

      squarePositions.push({ id: index, row, col, spanX, spanY })
    }

    // Sort by position to handle overlaps correctly
    squarePositions.sort((a, b) => {
      if (a.row !== b.row) return a.row - b.row
      return a.col - b.col
    })

    // Second pass: Place squares and handle overlaps
    for (const squarePos of squarePositions) {
      const { id, row, col, spanX, spanY } = squarePos

      // Skip if this position is already occupied by another square
      if (occupiedCells.has(`${row}-${col}`)) {
        continue
      }

      // Check if the square fits within grid bounds
      const fitsInGrid = row + spanY <= actualRows && col + spanX <= gridLayout.columns

      if (fitsInGrid) {
        // Check if all cells this square would occupy are available
        let canPlace = true
        for (let r = row; r < row + spanY; r++) {
          for (let c = col; c < col + spanX; c++) {
            if (occupiedCells.has(`${r}-${c}`)) {
              canPlace = false
              break
            }
          }
          if (!canPlace) break
        }

        if (canPlace) {
          // Mark all cells that this square will occupy
          for (let r = row; r < row + spanY; r++) {
            for (let c = col; c < col + spanX; c++) {
              occupiedCells.add(`${r}-${c}`)
            }
          }

          squareArray.push({
            id,
            row,
            col,
            spanX,
            spanY,
            isVisible: true,
            isMerged: spanX > 1 || spanY > 1,
            icon: icons[id % icons.length],
            color: squareColors[id] || defaultColorScheme,
            showIcon: squareIconVisibility[id] !== false, // Default to true
            title: squareTitles[id] || "",
            content: squareContents[id] || "",
            imageUrl: squareImageUrls[id] || "",
          })
        }
      }
    }

    return squareArray
  }, [
    totalSquares,
    gridLayout.columns,
    actualRows,
    customSizes,
    squareColors,
    defaultColorScheme,
    squareIconVisibility,
    squareTitles,
    squareContents,
    customPositions,
  ])

  // Calculate responsive styling with user-defined square size
  const getGridStyles = () => {
    if (typeof window === "undefined") return {}

    // Calculate gap based on square size (proportional)
    const gap = Math.max(4, Math.min(12, squareSize * 0.2))

    return {
      gridTemplateColumns: `repeat(${gridLayout.columns}, ${squareSize}px)`,
      gridTemplateRows: `repeat(${actualRows}, ${squareSize}px)`,
      gap: `${gap}px`,
      width: "fit-content",
      height: "fit-content",
    }
  }

  // Get color styles for a square
  const getSquareColorStyles = (colorName: string) => {
    const colorOption = colorOptions.find((c) => c.name === colorName);
    const defaultColor = colorOptions.find((c) => c.name === defaultColorScheme);
    return colorOption || defaultColor || colorOptions[0]; 
  };

  // Create and animate a clone of the card with progressive content scaling using FLIP
  const animateToCenter = (squareId: number) => {
    const originalElement = squareRefs.current[squareId];
    if (!originalElement || isAnimating) return;

    setIsAnimating(true);
    setAnimatingSquare(squareId);

    const squareData = squares.find((s) => s.id === squareId);
    if (!squareData) {
      setIsAnimating(false);
      setAnimatingSquare(null);
      return;
    }

    const firstRect = originalElement.getBoundingClientRect();

    const clone = originalElement.cloneNode(true) as HTMLDivElement;
    clone.id = `clone-${squareId}`;
    cloneRef.current = clone;

    const colorStyles = getSquareColorStyles(squareData.color);
    if (colorStyles) {
      // Attempt to get computed styles for dynamic Tailwind classes
      // This is a simplified approach; robustly getting Tailwind computed colors can be complex
      // It's often better if colorOptions directly contain the final hex/rgba values
      let bgColor = 'white';
      let textColor = 'black';
      const bgColorClass = colorStyles.square.split(' ').find(s => s.startsWith('bg-'));
      const textColorClass = colorStyles.iconColor.split(' ').find(s => s.startsWith('text-'));
      
      if (bgColorClass) {
        const tempDiv = document.createElement('div');
        tempDiv.className = bgColorClass;
        document.body.appendChild(tempDiv);
        bgColor = window.getComputedStyle(tempDiv).backgroundColor;
        document.body.removeChild(tempDiv);
      }
      if (textColorClass) {
        const tempSpan = document.createElement('span');
        tempSpan.className = textColorClass;
        document.body.appendChild(tempSpan);
        textColor = window.getComputedStyle(tempSpan).color;
        document.body.removeChild(tempSpan);
      }
      clone.style.backgroundColor = bgColor;
      clone.style.color = textColor;
    }
    
    clone.style.position = "fixed";
    clone.style.zIndex = "1000";
    clone.style.pointerEvents = "auto";
    clone.style.overflow = "hidden";
    clone.style.margin = '0';
    clone.style.gridColumn = "unset";
    clone.style.gridRow = "unset";
    clone.style.opacity = '0';

    const backdrop = document.createElement("div");
    backdrop.id = `backdrop-${squareId}`;
    Object.assign(backdrop.style, {
      position: "fixed", top: "0", left: "0", width: "100vw", height: "100vh",
      backgroundColor: "rgba(0, 0, 0, 0.65)", zIndex: "999", opacity: "0",
      transition: "opacity 0.4s cubic-bezier(0.23, 1, 0.32, 1)"
    });

    document.body.appendChild(backdrop);
    document.body.appendChild(clone);

    // Smoothly fade out the original element
    originalElement.style.transition = 'opacity 0.15s ease-out';
    originalElement.style.opacity = '0'; // Fade out completely
    originalElement.style.pointerEvents = 'none';

    const contentWrapper = document.createElement("div");
    contentWrapper.className = "content-wrapper-animated";
    Object.assign(contentWrapper.style, {
      width: "100%", height: "100%", position: "relative", display: "flex", flexDirection: "column", justifyContent: "center", alignItems: "center",
      opacity: "0", transform: "scale(0.9)",
      transition: "opacity 0.35s cubic-bezier(0.23, 1, 0.32, 1) 0.1s, transform 0.35s cubic-bezier(0.23, 1, 0.32, 1) 0.1s"
    });
    
    const originalInnerContent = originalElement.innerHTML; // Or a more specific selector for content
    contentWrapper.innerHTML = originalInnerContent;
    // Clear clone before appending wrapper to avoid duplication if children were moved from clone
    while (clone.firstChild) { clone.removeChild(clone.firstChild); }
    clone.appendChild(contentWrapper);

    const closeButton = document.createElement("button");
    closeButton.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>`;
    closeButton.className = "absolute top-3.5 right-3.5 w-9 h-9 bg-black/40 hover:bg-black/60 text-white rounded-full flex items-center justify-center shadow-xl transition-all duration-200 z-[1001] border-2 border-white/30 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-white/70";
    Object.assign(closeButton.style, {
      opacity: "0", transform: "scale(0.75)", pointerEvents: "auto",
      transition: "opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1) 0.2s, transform 0.3s cubic-bezier(0.23, 1, 0.32, 1) 0.2s"
    });
    closeButton.onclick = (e: MouseEvent) => { e.stopPropagation(); animateToOriginal(squareId); };
    clone.appendChild(closeButton);

    let targetWidth = window.innerWidth * 0.8;
    let targetHeight = targetWidth * (9 / 16);
    const maxHeight = window.innerHeight * 0.8;

    if (targetHeight > maxHeight) {
      targetHeight = maxHeight;
      targetWidth = targetHeight * (16 / 9);
    }
    const finalWidth = targetWidth;
    const finalHeight = targetHeight;
    const centerX = (window.innerWidth - finalWidth) / 2;
    const centerY = (window.innerHeight - finalHeight) / 2;
    const paddingValue = Math.min(finalWidth, finalHeight) * 0.04; 

    clone.style.transition = 'none';
    clone.style.width = `${finalWidth}px`;
    clone.style.height = `${finalHeight}px`;
    clone.style.top = `${centerY}px`;
    clone.style.left = `${centerX}px`;
    clone.style.padding = `${paddingValue}px`;
    clone.style.borderRadius = "20px"; 
    clone.style.boxShadow = "0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(0, 0, 0, 0.1)";
    clone.style.opacity = '1';

    const lastRect = clone.getBoundingClientRect();

    const deltaX = firstRect.left - lastRect.left;
    const deltaY = firstRect.top - lastRect.top;
    const deltaW = firstRect.width / finalWidth;
    const deltaH = firstRect.height / finalHeight;

    clone.style.transformOrigin = 'top left';
    clone.style.transform = `translate(${deltaX}px, ${deltaY}px) scale(${deltaW}, ${deltaH})`;
    clone.style.opacity = '0';
  
    clone.offsetHeight; 

    requestAnimationFrame(() => {
      clone.style.transition = `transform 0.4s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.4s cubic-bezier(0.23, 1, 0.32, 1), border-radius 0.4s cubic-bezier(0.23, 1, 0.32, 1), box-shadow 0.4s cubic-bezier(0.23, 1, 0.32, 1), padding 0.4s cubic-bezier(0.23, 1, 0.32, 1)`;
      clone.style.opacity = '1'; 
      clone.style.willChange = 'transform, opacity, border-radius, box-shadow, padding';
      clone.style.transform = `translate(0px, 0px) scale(1, 1)`;
    
      backdrop.style.opacity = "1";

      contentWrapper.style.opacity = "1";
      contentWrapper.style.transform = "scale(1)";
      closeButton.style.opacity = "1";
      closeButton.style.transform = "scale(1)";
    });

    // Remove transition from original element after its fade-out (0.15s)
    setTimeout(() => {
      if (originalElement) originalElement.style.transition = '';
    }, 150);

    // Final state update after main clone animation completes (0.4s)
    setTimeout(() => {
      if (clone) clone.style.willChange = 'auto';
      setFocusedSquare(squareId);
      setIsAnimating(false); 
      // Note: animatingSquare is reset in animateToOriginal or if animation is interrupted
    }, 400); 
  };

  const animateToOriginal = (squareId: number) => {
    const originalElement = squareRefs.current[squareId];
    const clone = cloneRef.current;
    const backdrop = document.getElementById(`backdrop-${squareId}`);

    if (!originalElement || !clone || isAnimating) {
      if (clone?.parentNode) clone.parentNode.removeChild(clone);
      if (backdrop?.parentNode) backdrop.parentNode.removeChild(backdrop);
      cloneRef.current = null;
      setFocusedSquare(null);
      if (originalElement) originalElement.style.opacity = "1";
      if (originalElement) originalElement.style.pointerEvents = "auto";
      setIsAnimating(false);
      setAnimatingSquare(null);
      return;
    }

    setIsAnimating(true);
    setAnimatingSquare(squareId); 

    const contentWrapper = clone.querySelector(".content-wrapper-animated") as HTMLElement | null;
    const closeButtonEl = clone.querySelector("button");

    if (contentWrapper) {
      contentWrapper.style.transition = "opacity 0.15s cubic-bezier(0.55, 0.055, 0.675, 0.19), transform 0.15s cubic-bezier(0.55, 0.055, 0.675, 0.19)";
      contentWrapper.style.opacity = "0";
      contentWrapper.style.transform = "scale(0.9)";
    }
    if (closeButtonEl) {
      closeButtonEl.style.transition = "opacity 0.15s cubic-bezier(0.55, 0.055, 0.675, 0.19), transform 0.15s cubic-bezier(0.55, 0.055, 0.675, 0.19)";
      closeButtonEl.style.opacity = "0";
      closeButtonEl.style.transform = "scale(0.75)";
    }
    if (backdrop) {
      backdrop.style.transition = "opacity 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19) 0.05s"; 
      backdrop.style.opacity = "0";
    }

    setTimeout(() => { 
      const firstRect = clone.getBoundingClientRect();
      clone.style.transformOrigin = 'top left';

      const targetRect = originalElement.getBoundingClientRect();
      const originalBorderRadius = window.getComputedStyle(originalElement).borderRadius;
      const originalBoxShadow = window.getComputedStyle(originalElement).boxShadow || "none"; 
      const originalPadding = window.getComputedStyle(originalElement).padding || "0px";

      const deltaX = targetRect.left - firstRect.left;
      const deltaY = targetRect.top - firstRect.top;
      const deltaW = targetRect.width / firstRect.width;
      const deltaH = targetRect.height / firstRect.height;
      
      // Start fading the original element back in
      originalElement.style.transition = 'opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1) 0.05s'; // Match clone's opacity easing and delay
      originalElement.style.opacity = '1';

      requestAnimationFrame(() => { 
        clone.style.willChange = 'transform, opacity, border-radius, box-shadow, padding';
        clone.style.transition = `
          transform 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19) 0.05s,
          opacity 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19) 0.05s,
          border-radius 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19) 0.05s,
          box-shadow 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19) 0.05s,
          padding 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19) 0.05s
        `;
        
        clone.style.transform = `translate(${deltaX}px, ${deltaY}px) scale(${deltaW}, ${deltaH})`;
        clone.style.opacity = '0'; 
        clone.style.borderRadius = originalBorderRadius;
        clone.style.padding = originalPadding;
        clone.style.boxShadow = originalBoxShadow;
      });

      setTimeout(() => {
        if (clone) clone.style.willChange = 'auto';
        if (clone?.parentNode) clone.parentNode.removeChild(clone);
        if (backdrop?.parentNode) backdrop.parentNode.removeChild(backdrop);
        cloneRef.current = null;

        setFocusedSquare(null);
        setAnimatingSquare(null);
        setIsAnimating(false);

        if (originalElement) {
          // Opacity is already transitioning, just ensure pointer events are restored
          originalElement.style.pointerEvents = "auto";
          // Remove transition after it's done to prevent interference (matches opacity fade-in duration)
          setTimeout(() => {
            if (originalElement) originalElement.style.transition = '';
          }, 350);
        }
        setFocusedSquare(null);
        setIsAnimating(false);
        setAnimatingSquare(null);
      }, 350); 
    }, 150); 
  };

  // Handle close button click (now handled in the clone)
  const handleCloseFocus = () => {
    if (focusedSquare !== null) {
      animateToOriginal(focusedSquare)
    }
  }

  // Handle square click
  const handleSquareClick = (squareId: number) => {
    if (isAnimating) return // Prevent action if an animation is in progress

    if (editMode) {
      setSelectedSquare((prev: number | null) => (prev === squareId ? null : squareId)) // Toggle selection
      setFocusedSquare(null) // Ensure no square is focused for animation in edit mode
    } else {
      // If a square is already focused, animate it back first
      if (focusedSquare !== null && focusedSquare !== squareId) {
        animateToOriginal(focusedSquare)
        // Potentially wait for animateToOriginal to complete before animating new one,
        // or manage state to queue animations if complex interactions are desired.
        // For now, we'll allow immediate animation to the new square.
      }
      animateToCenter(squareId)
    }
  }

  // Toggle edit mode
  const toggleEditMode = () => {
    setEditMode(!editMode)
    // Clear states when switching modes
    if (!editMode) {
      // Entering edit mode - clear focus and return animated square
      if (focusedSquare !== null) {
        animateToOriginal(focusedSquare)
      }
    } else {
      // Exiting edit mode - clear edit states
      setSelectedSquare(null)
      setShowColorPicker(false)
      setShowTextEditor(false)
      setShowIconBgColorPicker(false)
    }
  }

  // Apply custom size to selected square
  const applyCustomSize = () => {
    if (selectedSquare === null) return

    const maxSpanX = Math.min(tempSpanX, gridLayout.columns)
    const maxSpanY = Math.min(tempSpanY, actualRows)

    setCustomSizes((prev: Record<number, { spanX: number; spanY: number }>) => ({
      ...prev,
      [selectedSquare]: {
        spanX: Math.max(1, maxSpanX),
        spanY: Math.max(1, maxSpanY),
      },
    }))
  }

  // Apply color to selected square
  const applyColor = (colorName: string) => {
    if (selectedSquare === null) return

    setSquareColors((prev: Record<number, string>) => ({
      ...prev,
      [selectedSquare]: colorName,
    }))
    setShowColorPicker(false)
  }

  // Apply icon background color to selected square
  const applyIconBgColor = (colorName: string) => {
    if (selectedSquare === null) return

    setIconBackgroundColors((prev: Record<number, string>) => ({
      ...prev,
      [selectedSquare]: colorName,
    }))
    setShowIconBgColorPicker(false)
  }

  // Toggle icon visibility for selected square
  const toggleIconVisibility = () => {
    if (selectedSquare === null) return

    setSquareIconVisibility((prev: Record<number, boolean>) => ({
      ...prev,
      [selectedSquare]: !prev[selectedSquare],
    }))
  }

  // Update title for selected square
  const updateTitle = (title: string) => {
    if (selectedSquare === null) return

    setSquareTitles((prev: Record<number, string>) => ({
      ...prev,
      [selectedSquare]: title,
    }))
  }

  // Update content for selected square
  const updateContent = (content: string) => {
    if (selectedSquare !== null) {
      setSquareContents((prev) => ({ ...prev, [selectedSquare]: content }))
    }
  }

  // Apply image URL to selected square
  const applyImageUrl = (imageUrl: string) => {
    if (selectedSquare !== null) {
      setSquareImageUrls((prev) => ({ ...prev, [selectedSquare]: imageUrl.trim() }))
      // Hide icon when an image is applied
      setSquareIconVisibility((prev) => ({ ...prev, [selectedSquare]: false }))
    }
  }

  // Reset square to default size and color
  const resetSquareSize = () => {
    if (selectedSquare === null) return

    setCustomSizes((prev: Record<number, { spanX: number; spanY: number }>) => {
      const newSizes = { ...prev }
      delete newSizes[selectedSquare]
      return newSizes
    })
    setSquareColors((prev: Record<number, string>) => {
      const newColors = { ...prev }
      delete newColors[selectedSquare]
      return newColors
    })
    setSquareIconVisibility((prev: Record<number, boolean>) => {
      const newVisibility = { ...prev }
      delete newVisibility[selectedSquare]
      return newVisibility
    })
    setSquareTitles((prev: Record<number, string>) => {
      const newTitles = { ...prev }
      delete newTitles[selectedSquare]
      return newTitles
    })
    setSquareContents((prev: Record<number, string>) => {
      const newContents = { ...prev }
      delete newContents[selectedSquare]
      return newContents
    })
    setSquareImageUrls((prev: Record<number, string | undefined>) => {
      const newImageUrls = { ...prev }
      delete newImageUrls[selectedSquare]
      return newImageUrls
    })
    setCustomPositions((prev: Record<number, SquarePosition>) => {
      const newPositions = { ...prev }
      delete newPositions[selectedSquare]
      return newPositions
    })
    setIconBackgroundColors((prev: Record<number, string>) => {
      const newIconBgColors = { ...prev }
      delete newIconBgColors[selectedSquare]
      return newIconBgColors
    })
    setTempSpanX(1)
    setTempSpanY(1)
  }

  // Reset all squares
  const resetAllSizes = () => {
    // Return any animated square to original position first
    if (focusedSquare !== null) {
      animateToOriginal(focusedSquare)
    }

    setCustomSizes({})
    setSquareColors({})
    setSquareIconVisibility({})
    setSquareTitles({})
    setSquareContents({})
    setSquareImageUrls({})
    setCustomPositions({})
    setIconBackgroundColors({})
    setSelectedSquare(null)
    setTempSpanX(1)
    setTempSpanY(1)
    setShowColorPicker(false)
    setShowTextEditor(false)
    setShowIconBgColorPicker(false)
  }

  // Recalculate grid layout manually
  const recalculateLayout = () => {
    const width = window.innerWidth
    const height = window.innerHeight

    const availableWidth = Math.min(width * 0.9, 1200)
    const availableHeight = height * 0.7

    // Calculate gap based on square size (proportional)
    const gap = Math.max(4, Math.min(12, squareSize * 0.2))

    // Calculate how many squares can fit in each dimension
    const maxColumns = Math.floor((availableWidth + gap) / (squareSize + gap))
    const maxRows = Math.floor((availableHeight + gap) / (squareSize + gap))

    // Ensure we have at least enough space for all squares
    const minColumns = Math.max(4, Math.ceil(Math.sqrt(totalSquares * 0.8)))
    const minRows = Math.max(4, Math.ceil(totalSquares / minColumns))

    // Choose the layout that fits best within constraints
    const columns = Math.max(minColumns, Math.min(maxColumns, 24))
    const rows = Math.max(minRows, Math.ceil(totalSquares / columns))

    // Reset custom positions when recalculating layout
    setCustomPositions({})
    setGridLayout({ columns, rows })
  }

  // Clean up customizations for squares that no longer exist
  useEffect(() => {
    const cleanupRemovedSquares = () => {
      // Clean up customizations for squares beyond the current totalSquares
      setCustomSizes((prev: Record<number, { spanX: number; spanY: number }>) => {
        const cleaned = { ...prev }
        Object.keys(cleaned).forEach((key) => {
          const numericKey = Number.parseInt(key);
          if (numericKey >= totalSquares) {
            delete cleaned[numericKey];
          }
        })
        return cleaned
      })

      setSquareColors((prev: Record<number, string>) => {
        const cleaned = { ...prev }
        Object.keys(cleaned).forEach((key) => {
          const numericKey = Number.parseInt(key);
          if (numericKey >= totalSquares) {
            delete cleaned[numericKey];
          }
        })
        return cleaned
      })

      setSquareIconVisibility((prev: Record<number, boolean>) => {
        const cleaned = { ...prev }
        Object.keys(cleaned).forEach((key) => {
          const numericKey = Number.parseInt(key);
          if (numericKey >= totalSquares) {
            delete cleaned[numericKey];
          }
        })
        return cleaned
      })

      setSquareTitles((prev: Record<number, string>) => {
        const cleaned = { ...prev }
        Object.keys(cleaned).forEach((key) => {
          const numericKey = Number.parseInt(key);
          if (numericKey >= totalSquares) {
            delete cleaned[numericKey];
          }
        })
        return cleaned
      })

      setSquareContents((prev: Record<number, string>) => {
        const cleaned = { ...prev }
        Object.keys(cleaned).forEach((key) => {
          const numericKey = Number.parseInt(key);
          if (numericKey >= totalSquares) {
            delete cleaned[numericKey];
          }
        })
        return cleaned
      })

      setCustomPositions((prev: Record<number, SquarePosition>) => {
        const cleaned = { ...prev }
        Object.keys(cleaned).forEach((key) => {
          const numericKey = Number.parseInt(key);
          if (numericKey >= totalSquares) {
            delete cleaned[numericKey];
          }
        })
        return cleaned
      })

      setIconBackgroundColors((prev: Record<number, string>) => {
        const cleaned = { ...prev }
        Object.keys(cleaned).forEach((key) => {
          const numericKey = Number.parseInt(key);
          if (numericKey >= totalSquares) {
            delete cleaned[numericKey];
          }
        })
        return cleaned
      })

      // Deselect square if it no longer exists
      if (selectedSquare !== null && selectedSquare >= totalSquares) {
        setSelectedSquare(null)
      }
      if (focusedSquare !== null && focusedSquare >= totalSquares) {
        animateToOriginal(focusedSquare)
      }
    }

    cleanupRemovedSquares()
  }, [totalSquares, selectedSquare, focusedSquare])

  // Add keyboard navigation for closing the focused card
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape" && focusedSquare !== null && !editMode) {
        animateToOriginal(focusedSquare)
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => {
      window.removeEventListener("keydown", handleKeyDown)
    }
  }, [focusedSquare, editMode])

  // Check if a square is large enough for text
  const isLargeEnoughForText = (spanX: number, spanY: number) => {
    return spanX >= 2 || spanY >= 2
  }

  // Toggle rearrange mode
  const toggleRearrangeMode = () => {
    // Return any animated square to original position first
    if (focusedSquare !== null) {
      animateToOriginal(focusedSquare)
    }

    setRearrangeMode(!rearrangeMode)
    setSelectedSquare(null)
    setShowColorPicker(false)
    setShowTextEditor(false)
    setShowIconBgColorPicker(false)
  }

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, squareId: number) => {
    if (!rearrangeMode) return

    e.dataTransfer.setData("text/plain", squareId.toString())
    e.dataTransfer.effectAllowed = "move"

    // Add a small delay to make the drag image appear
    setTimeout(() => {
      setIsDragging(true)
      setDraggedSquare(squareId)
    }, 0)
  }

  // Handle drag over
  const handleDragOver = (e: React.DragEvent, squareId: number) => {
    if (!rearrangeMode || draggedSquare === null) return

    e.preventDefault()
    e.dataTransfer.dropEffect = "move"

    // Update the drop target
    if (dropTarget !== squareId) {
      setDropTarget(squareId)
    }
  }

  // Handle drag end
  const handleDragEnd = () => {
    setIsDragging(false)
    setDraggedSquare(null)
    setDropTarget(null)
  }

  // Handle drop
  const handleDrop = (e: React.DragEvent, targetSquareId: number) => {
    if (!rearrangeMode) return

    e.preventDefault()
    const sourceSquareId = Number(e.dataTransfer.getData("text/plain"))

    if (sourceSquareId === targetSquareId) {
      setIsDragging(false)
      setDraggedSquare(null)
      setDropTarget(null)
      return
    }

    // Find the source and target squares
    const sourceSquare = squares.find((s) => s.id === sourceSquareId)
    const targetSquare = squares.find((s) => s.id === targetSquareId)

    if (!sourceSquare || !targetSquare) {
      setIsDragging(false)
      setDraggedSquare(null)
      setDropTarget(null)
      return
    }

    // Swap positions
    setCustomPositions((prev: Record<number, SquarePosition>) => {
      const newPositions = { ...prev }

      // Set source square to target position
      newPositions[sourceSquareId] = {
        row: targetSquare.row,
        col: targetSquare.col,
      }

      // Set target square to source position
      newPositions[targetSquareId] = {
        row: sourceSquare.row,
        col: sourceSquare.col,
      }

      return newPositions
    })

    setIsDragging(false)
    setDraggedSquare(null)
    setDropTarget(null)
  }

  const gridStyles = getGridStyles()

  return (
    <div className={cn("w-full flex flex-col items-center px-4 py-8", className)}>
      {/* Control Panel */}
      <div className="mb-6 p-4 bg-white rounded-lg border border-gray-200 shadow-sm w-full max-w-5xl">
        <div className="flex flex-col gap-4">
          {/* Main Controls Row */}
          <div className="flex flex-col sm:flex-row items-center gap-4 pb-4 border-b border-gray-200">
            <div className="flex items-center gap-2">
              <Grid className="w-5 h-5 text-gray-500" />
              <Label htmlFor="totalSquares" className="text-sm font-medium">
                Grid Size:
              </Label>
            </div>
            <div className="flex items-center gap-2">
              <Input
                id="totalSquares"
                type="number"
                min="4"
                max="500"
                value={totalSquares}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setTotalSquares(Number.parseInt(e.target.value) || 96)}
                className="w-20 h-8"
              />
              <span className="text-sm text-gray-500">squares</span>
            </div>

            {/* Square Size Control */}
            <div className="flex items-center gap-2 ml-4">
              <Maximize className="w-4 h-4 text-gray-500" />
              <Label htmlFor="squareSize" className="text-sm font-medium whitespace-nowrap">
                Square Size:
              </Label>
              <div className="w-32">
                <Slider
                  id="squareSize"
                  min={24}
                  max={120}
                  step={4}
                  value={[squareSize]}
                  onValueChange={(value: number[]) => setSquareSize(value[0])}
                />
              </div>
              <span className="text-sm text-gray-500 w-12">{squareSize}px</span>
            </div>

            {/* Edit Mode Toggle */}
            <Button
              onClick={toggleEditMode}
              variant={editMode ? "default" : "outline"}
              size="sm"
              className={cn("ml-4", editMode && "bg-green-600 hover:bg-green-700 text-white")}
            >
              <Edit3 className="w-4 h-4 mr-1" />
              {editMode ? "Exit Edit Mode" : "Edit Mode"}
            </Button>

            {/* Recalculate Layout Button */}
            <Button onClick={recalculateLayout} variant="outline" size="sm" className="ml-2">
              <RotateCcw className="w-4 h-4 mr-1" />
              Optimize Layout
            </Button>

            {/* Rearrange Mode Button */}
            <Button
              onClick={toggleRearrangeMode}
              variant={rearrangeMode ? "default" : "outline"}
              size="sm"
              className={cn(rearrangeMode && "bg-blue-600 hover:bg-blue-700")}
            >
              <MoveHorizontal className="w-4 h-4 mr-1" />
              {rearrangeMode ? "Exit Rearrange Mode" : "Rearrange Squares"}
            </Button>
          </div>

          {/* Icon Size Control */}
          <div className="flex flex-col sm:flex-row items-center gap-4 pb-4 border-b border-gray-200">
            <div className="flex items-center gap-2 ml-4">
              <Label htmlFor="iconSize" className="text-sm font-medium whitespace-nowrap">
                Icon Size:
              </Label>
              <div className="w-32">
                <Slider
                  id="iconSize"
                  min={16}
                  max={Math.min(48, squareSize * 0.8)}
                  step={4}
                  value={[iconSize]}
                  onValueChange={(value: number[]) => setIconSize(value[0])}
                />
              </div>
              <span className="text-sm text-gray-500 w-8">{iconSize}px</span>
            </div>
          </div>

          {/* Mode Status and Instructions */}
          <div className="flex items-center justify-between bg-gray-50 p-3 rounded-md border border-gray-200">
            <div className="flex items-center">
              <div className={cn("w-3 h-3 rounded-full mr-2", editMode ? "bg-green-500" : "bg-blue-500")} />
              <span className="text-sm font-medium">{editMode ? "Edit Mode Active" : "View Mode Active"}</span>
              <span className="text-sm text-gray-600 ml-2">
                {editMode
                  ? "Click squares to customize them"
                  : "Click squares to create an animated clone in 16:9 format"}
              </span>
            </div>
            {(selectedSquare !== null || focusedSquare !== null) && (
              <span className="text-sm text-gray-600">
                {selectedSquare !== null
                  ? `Square #${selectedSquare + 1} Selected`
                  : `Square #${focusedSquare! + 1} Focused`}
              </span>
            )}
          </div>

          {/* Edit Controls - Only visible in edit mode */}
          {editMode && !rearrangeMode && (
            <div className="flex flex-col sm:flex-row items-center gap-4">
              <div className="text-sm font-medium text-gray-700">
                {selectedSquare !== null ? `Editing Square #${selectedSquare + 1}` : "Select a square to edit"}
              </div>

              {selectedSquare !== null && (
                <>
                  <div className="flex items-center gap-2">
                    <Label htmlFor="spanX" className="text-sm">
                      Width:
                    </Label>
                    <Input
                      id="spanX"
                      type="number"
                      min="1"
                      max={gridLayout.columns}
                      value={tempSpanX}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setTempSpanX(Number.parseInt(e.target.value) || 1)}
                      className="w-16 h-8"
                    />
                  </div>

                  <div className="flex items-center gap-2">
                    <Label htmlFor="spanY" className="text-sm">
                      Height:
                    </Label>
                    <Input
                      id="spanY"
                      type="number"
                      min="1"
                      max={actualRows}
                      value={tempSpanY}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) => setTempSpanY(Number.parseInt(e.target.value) || 1)}
                      className="w-16 h-8"
                    />
                  </div>

                  <Button onClick={applyCustomSize} size="sm">
                    Apply Size
                  </Button>

                  <Button onClick={() => setShowColorPicker(!showColorPicker)} variant="outline" size="sm">
                    <Palette className="w-4 h-4 mr-1" />
                    Color
                  </Button>

                  <Button
                    onClick={toggleIconVisibility}
                    variant="outline"
                    size="sm"
                    className={cn(
                      selectedSquare !== null && squareIconVisibility[selectedSquare] === false ? "bg-gray-200" : "",
                    )}
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    {selectedSquare !== null && squareIconVisibility[selectedSquare] === false ? "Show" : "Hide"} Icon
                  </Button>

                  <Button onClick={() => setShowIconBgColorPicker(!showIconBgColorPicker)} variant="outline" size="sm">
                    Icon BG
                  </Button>

                  {/* Image URL Input */}
                  <div className="flex items-center gap-2">
                    <Label htmlFor="imageUrl" className="text-sm whitespace-nowrap">
                      Image URL:
                    </Label>
                    <Input
                      id="imageUrl"
                      type="text"
                      placeholder="https://example.com/image.png"
                      value={tempImageUrl}
                      onChange={(e) => setTempImageUrl(e.target.value)}
                      className="w-48 h-8"
                    />
                    <Button
                      onClick={() => {
                        if (tempImageUrl) {
                          applyImageUrl(tempImageUrl)
                          setTempImageUrl("") // Clear input after applying
                        }
                      }}
                      size="sm"
                      disabled={!tempImageUrl.trim()}
                    >
                      <ImageIconLucide className="w-4 h-4 mr-1" />
                      Apply Image
                    </Button>
                  </div>

                  {isLargeEnoughForText(tempSpanX, tempSpanY) && (
                    <Button onClick={() => setShowTextEditor(!showTextEditor)} variant="outline" size="sm">
                      <Type className="w-4 h-4 mr-1" />
                      {showTextEditor ? "Hide" : "Edit"} Text
                    </Button>
                  )}

                  <Button onClick={resetSquareSize} variant="outline" size="sm">
                    Reset
                  </Button>
                </>
              )}

              <Button onClick={resetAllSizes} variant="outline" size="sm">
                Reset All
              </Button>

              <Button
                onClick={() => {
                  setSelectedSquare(null)
                  setFocusedSquare(null)
                }}
                variant="ghost"
                size="sm"
                className="ml-auto"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          )}

          {/* Rearrange Mode Instructions */}
          {rearrangeMode && (
            <div className="flex items-center justify-between bg-blue-50 p-3 rounded-md border border-blue-200">
              <div className="flex items-center">
                <Info className="w-5 h-5 text-blue-500 mr-2" />
                <span className="text-sm text-blue-700">
                  Drag and drop squares to rearrange them. Click "Exit Rearrange Mode" when finished.
                </span>
              </div>
              <Button onClick={() => setCustomPositions({})} variant="outline" size="sm" className="ml-2">
                Reset Positions
              </Button>
            </div>
          )}

          {/* Color Picker - Only visible in edit mode */}
          {showColorPicker && selectedSquare !== null && editMode && !rearrangeMode && (
            <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-md">
              {colorOptions.map((color) => (
                <button
                  key={color.name}
                  onClick={() => applyColor(color.name)}
                  className={cn(
                    "w-8 h-8 rounded border-2 transition-all",
                    color.square,
                    squareColors[selectedSquare] === color.name ? "border-gray-800 scale-110" : "border-gray-300",
                  )}
                  title={color.name}
                />
              ))}
            </div>
          )}

          {/* Icon Background Color Picker - Only visible in edit mode */}
          {showIconBgColorPicker && selectedSquare !== null && editMode && !rearrangeMode && (
            <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-md">
              {iconBgColorOptions.map((color) => (
                <button
                  key={color.name}
                  onClick={() => applyIconBgColor(color.name)}
                  className={cn(
                    "w-8 h-8 rounded border-2 transition-all",
                    color.iconBg,
                    iconBackgroundColors[selectedSquare] === color.name
                      ? "border-gray-800 scale-110"
                      : "border-gray-300",
                  )}
                  title={color.name}
                />
              ))}
            </div>
          )}

          {/* Text Editor - Only visible in edit mode */}
          {showTextEditor &&
            selectedSquare !== null &&
            isLargeEnoughForText(tempSpanX, tempSpanY) &&
            editMode &&
            !rearrangeMode && (
              <div className="flex flex-col gap-3 p-3 bg-gray-50 rounded-md">
                <div className="flex flex-col gap-1">
                  <Label htmlFor="squareTitle" className="text-sm font-medium">
                    Title:
                  </Label>
                  <Input
                    id="squareTitle"
                    value={squareTitles[selectedSquare] || ""}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateTitle(e.target.value)}
                    placeholder="Enter title"
                    className="h-8"
                  />
                </div>
                <div className="flex flex-col gap-1">
                  <Label htmlFor="squareContent" className="text-sm font-medium">
                    Content:
                  </Label>
                  <textarea
                    id="squareContent"
                    value={squareContents[selectedSquare] || ""}
                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => updateContent(e.target.value)}
                    placeholder="Enter content"
                    className="w-full h-20 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            )}
        </div>
      </div>

      {/* Grid Container */}
      <div
        ref={gridContainerRef}
        className="relative overflow-visible rounded-lg bg-white/50 backdrop-blur-sm border border-gray-200/50 p-4 sm:p-6 lg:p-8"
      >
        <div className="grid justify-center items-start mx-auto" style={gridStyles}>
          {squares.map((square: SquareData) => {
            if (!square.isVisible) return null

            const IconComponent = square.icon
            const isSelected = selectedSquare === square.id
            const isFocused = focusedSquare === square.id
            const isAnimating = animatingSquare === square.id
            const isDragged = draggedSquare === square.id
            const isDropTarget = dropTarget === square.id
            const colorStyles = getSquareColorStyles(square.color)
            const hasText = isLargeEnoughForText(square.spanX, square.spanY) && (square.title || square.content)
            const shouldBlur = focusedSquare !== null && !isFocused && !editMode && !isAnimating

            return (
              <div
                key={`square-${square.id}`}
                ref={(el) => { squareRefs.current[square.id] = el; }}
                className={cn(
                  "relative rounded-xl transition-all duration-300 overflow-hidden",
                  colorStyles.square,
                  isSelected && editMode && "border-green-600 border-2 shadow-lg",
                  rearrangeMode && "cursor-move",
                  !rearrangeMode && !isAnimating && "cursor-pointer",
                  isDragged && "opacity-50 border-dashed border-2 border-gray-400",
                  isDropTarget && "ring-2 ring-blue-500 ring-inset",
                  shouldBlur && "blur-sm opacity-50 scale-95",
                  isFocused && "ring-2 ring-blue-400 ring-opacity-50",
                )}
                style={{
                  gridColumn: `span ${square.spanX}`,
                  gridRow: `span ${square.spanY}`,
                  aspectRatio: `${square.spanX} / ${square.spanY}`,
                }}
                onClick={() => handleSquareClick(square.id)}
                draggable={rearrangeMode}
                onDragStart={(e) => handleDragStart(e, square.id)}
                onDragOver={(e) => handleDragOver(e, square.id)}
                onDragEnd={handleDragEnd}
                onDrop={(e) => handleDrop(e, square.id)}
              >
                {/* Content wrapper */}
                <div className="w-full h-full relative">
                  {/* Conditionally render icon only if showIcon is true */}
                  {square.showIcon && (
                    <>
                      {/* Icon background */}
                      <div
                        className={cn(
                          "absolute rounded-md opacity-25 transition-all duration-300",
                          iconBackgroundColors[square.id] || colorStyles.iconBg,
                        )}
                        style={
                          square.spanX === 1 && square.spanY === 1
                            ? {
                                width: `${iconSize + 8}px`,
                                height: `${iconSize + 8}px`,
                                top: "50%",
                                left: "50%",
                                transform: "translate(-50%, -50%)",
                              }
                            : {
                                width: `${iconSize + 8}px`,
                                height: `${iconSize + 8}px`,
                                top: "8px",
                                left: "8px",
                              }
                        }
                      />

                      {/* Icon */}
                      <div
                        className={cn(
                          "absolute flex items-center justify-center transition-all duration-300",
                          colorStyles.iconColor,
                        )}
                        style={
                          square.spanX === 1 && square.spanY === 1
                            ? {
                                width: `${iconSize}px`,
                                height: `${iconSize}px`,
                                top: "50%",
                                left: "50%",
                                transform: "translate(-50%, -50%)",
                              }
                            : {
                                width: `${iconSize}px`,
                                height: `${iconSize}px`,
                                top: "12px",
                                left: "12px",
                              }
                        }
                      >
                        <IconComponent className="w-full h-full" />
                      </div>
                    </>
                  )}

                  {/* Image Display */}
                  {square.imageUrl && !square.showIcon && (
                    <img
                      src={square.imageUrl}
                      alt={square.title || `Image for square ${square.id}`}
                      className="absolute inset-0 w-full h-full object-cover rounded-lg pointer-events-none"
                      style={{
                        opacity: focusedSquare === square.id || animatingSquare === square.id ? 0 : 1,
                      }}
                    />
                  )}

                  {/* Title and Content */}
                  {hasText && (
                    <div className="absolute bottom-0 left-0 right-0 p-2 overflow-hidden">
                      {square.title && <h3 className="text-sm font-bold text-gray-800 truncate">{square.title}</h3>}
                      {square.content && <p className="text-xs text-gray-600 line-clamp-3 mt-1">{square.content}</p>}
                    </div>
                  )}
                </div>

                {/* Edit indicator for selected square in edit mode */}
                {isSelected && editMode && (
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center shadow-lg z-20">
                    <Edit3 className="w-3 h-3" />
                  </div>
                )}

                {/* Rearrange indicator */}
                {rearrangeMode && (
                  <div className="absolute top-1 right-1 bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs z-10">
                    <MoveHorizontal className="w-3 h-3" />
                  </div>
                )}

                {/* Selection indicator for edit mode */}
                {isSelected && editMode && (
                  <div className="absolute inset-0 bg-green-600/10 rounded-xl pointer-events-none" />
                )}
              </div>
            )
          })}
        </div>

        {/* Grid Info Display */}
        <div className="mt-4 text-center text-sm text-gray-500">
          <span className="font-medium">
            {gridLayout.columns} × {actualRows} Layout
          </span>
          <span className="mx-2">•</span>
          <span>{totalSquares} Total Squares</span>
          <span className="mx-2">•</span>
          <span>{squares.filter((s) => s.isVisible).length} Visible</span>
          <span className="mx-2">•</span>
          <span>{Object.keys(customSizes).length} Customized</span>
          <span className="mx-2">•</span>
          <span>{Object.keys(customPositions).length} Rearranged</span>
          <span className="mx-2">•</span>
          <span>{Object.keys(iconBackgroundColors).length} Icon BG</span>
          <span className="mx-2">•</span>
          <span>{squareSize}px Square Size</span>
          <span className="mx-2">•</span>
          <span className={cn("font-medium", editMode ? "text-green-600" : "text-blue-600")}>
            {editMode ? "Edit Mode" : "View Mode"}
          </span>
          {focusedSquare !== null && (
            <>
              <span className="mx-2">•</span>
              <span className="text-blue-600 font-medium">Square #{focusedSquare + 1} Cloned & Centered</span>
            </>
          )}
        </div>

        <div className="mt-2 text-center text-xs text-gray-400">
          {rearrangeMode
            ? "Drag and drop squares to rearrange them • Click 'Exit Rearrange Mode' when finished"
            : editMode
              ? "Edit Mode: Click squares to customize them • Toggle 'Edit Mode' to switch to view mode"
              : "View Mode: Click squares to create animated clones in 16:9 format • Press ESC to close"}
        </div>
      </div>
    </div>
  )
}
