import * as THREE from 'three';
import { Effect } from 'postprocessing';

const fragment = `
uniform sampler2D uCharacters;
uniform float uCharactersCount;
uniform float uCellSize;
uniform bool uInvert;
uniform vec3 uColor;
uniform vec3 uBackgroundColor;

const vec2 SIZE = vec2(16.);

vec3 greyscale(vec3 color, float strength) {
    float g = dot(color, vec3(0.299, 0.587, 0.114));
    return mix(color, vec3(g), strength);
}

vec3 greyscale(vec3 color) {
    return greyscale(color, 1.0);
}

void mainImage(const in vec4 inputColor, const in vec2 uv, out vec4 outputColor) {
    vec2 cell = resolution / uCellSize;
    vec2 grid = 1.0 / cell;
    vec2 pixelizedUV = grid * (0.5 + floor(uv / grid));
    vec4 pixelized = texture2D(inputBuffer, pixelizedUV);
    float greyscaled = greyscale(pixelized.rgb).r;

    if (uInvert) {
        greyscaled = 1.0 - greyscaled;
    }

    float characterIndex = floor((uCharactersCount - 1.0) * greyscaled);
    vec2 characterPosition = vec2(mod(characterIndex, SIZE.x), floor(characterIndex / SIZE.y));
    vec2 offset = vec2(characterPosition.x, -characterPosition.y) / SIZE;
    vec2 charUV = mod(uv * (cell / SIZE), 1.0 / SIZE) - vec2(0., 1.0 / SIZE) + offset;
    vec4 asciiCharacter = texture2D(uCharacters, charUV);

    // Mix background and foreground colors based on the character mask (charColor.r)
    // charColor.r is 1 for character, 0 for background of the character cell in the atlas
    outputColor.rgb = mix(uBackgroundColor, uColor, asciiCharacter.r);
    outputColor.a = 1.0; // Ensure output is opaque cells fully opaque
}
`;

export interface IASCIIEffectProps {
    characters?: string;
    fontSize?: number;
    cellSize?: number;
    color?: string;
    backgroundColor?: string; 
    invert?: boolean;
}

export class AsciiEffect extends Effect {
    constructor({
        characters = ` .:,'-^=*+?!|0#X%WM@`,
        fontSize = 54,
        cellSize = 16,
        color = '#ffffff',
        backgroundColor = '#000000', 
        invert = false
    }: IASCIIEffectProps = {}) {
        const uniforms = new Map<string, THREE.Uniform>([
            ['uCharacters', new THREE.Uniform(new THREE.Texture())],
            ['uCellSize', new THREE.Uniform(cellSize)],
            ['uCharactersCount', new THREE.Uniform(characters.length)],
            ['uColor', new THREE.Uniform(new THREE.Color(color))],
            ['uBackgroundColor', new THREE.Uniform(new THREE.Color(backgroundColor))],
            ['uInvert', new THREE.Uniform(invert)],
        ]);

        super('AsciiEffect', fragment, { uniforms });

        const charactersTextureUniform = this.uniforms.get('uCharacters');

        if (charactersTextureUniform) {
            const texture = this.createCharactersTexture(characters, fontSize);
            if (texture) {
                charactersTextureUniform.value = texture;
            }
        }
    }

    /** Draws the characters on a Canvas and returns a texture */
    public createCharactersTexture(characters: string, fontSize: number): THREE.Texture | null {
        if (typeof document === 'undefined') {
            // Return a placeholder or null if not in a browser environment
            // This might mean the effect won't work correctly during SSR, but won't crash.
            // A proper fix might involve lazy loading the effect or creating texture on client.
            console.warn('AsciiEffect: document is not defined, cannot create characters texture on server.');
            // Return a minimal 1x1 white texture as a fallback to avoid further errors
            const placeholderCanvas = { width: 1, height: 1, getContext: () => ({ clearRect: () => {}, fillText: () => {} }) }; // Mock canvas
            // @ts-ignore
            const placeholderTexture = new THREE.CanvasTexture(placeholderCanvas as HTMLCanvasElement);
            return placeholderTexture; 
        }
        const canvas = document.createElement('canvas');

        const SIZE = 1024;
        const MAX_PER_ROW = 16;
        const CELL = SIZE / MAX_PER_ROW;

        canvas.width = canvas.height = SIZE;

        const texture = new THREE.CanvasTexture(
            canvas,
            undefined,
            THREE.RepeatWrapping,
            THREE.RepeatWrapping,
            THREE.NearestFilter,
            THREE.NearestFilter
        );

        const context = canvas.getContext('2d');

        if (!context) {
            throw new Error('Context not available');
        }

        context.clearRect(0, 0, SIZE, SIZE);
        context.font = `${fontSize}px monospace`;
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillStyle = '#fff';

        for (let i = 0; i < characters.length; i++) {
            const char = characters[i];
            const x = i % MAX_PER_ROW;
            const y = Math.floor(i / MAX_PER_ROW);

            context.fillText(char, x * CELL + CELL / 2, y * CELL + CELL / 2);
        }

        texture.needsUpdate = true;

        return texture;
    }
}
