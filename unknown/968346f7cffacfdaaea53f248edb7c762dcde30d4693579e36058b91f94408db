"use client"

import { Building2 } from "lucide-react"

interface CityComponentProps {
  title?: string
}

export default function CityComponent({ title = "City Skyline" }: CityComponentProps) {
  return (
    <div className="w-full max-w-3xl mx-auto p-6 bg-gradient-to-b from-purple-100 to-indigo-50 rounded-lg shadow-md">
      <div className="flex items-center justify-center mb-6">
        <Building2 className="h-16 w-16 text-indigo-600" />
      </div>
      <h2 className="text-3xl font-bold text-center text-indigo-800 mb-4">{title}</h2>
      <p className="text-xl text-indigo-700 text-center">
        Towering skyscrapers forming a magnificent silhouette against the vibrant urban backdrop.
      </p>
    </div>
  )
}
