"use client"
import { <PERSON><PERSON><PERSON> } from "lucide-react"
import type React from "react"

import { GlowEffect } from "@/components/glow-effect"
import { cn } from "@/lib/utils"
import { cva, type VariantProps } from "class-variance-authority"

/**
 * FlowButton Component
 *
 * A modern, animated button with a flowing glow effect that can be dynamically sized.
 *
 * @example
 * // Basic usage with default size
 * <FlowButton text="Get Started" />
 *
 * @example
 * // Using a predefined size
 * <FlowButton text="Small Button" size="sm" />
 * <FlowButton text="Large Button" size="lg" />
 *
 * @example
 * // Using custom dimensions
 * <FlowButton text="Custom Width" width="300px" />
 * <FlowButton text="Custom Size" width="250px" height="70px" />
 *
 * @example
 * // Using className for additional styling
 * <FlowButton text="Custom Style" className="my-4" />
 */

const buttonVariants = cva(
  "relative flex items-center gap-1 overflow-hidden rounded-[100px] border-[1.5px] border-[#333333]/40 bg-white/90 backdrop-blur-sm text-sm font-semibold text-[#111111] cursor-pointer transition-all duration-[600ms] ease-[cubic-bezier(0.23,1,0.32,1)] group-hover:text-black group-hover:rounded-[12px] active:scale-[0.95] z-10",
  {
    variants: {
      size: {
        sm: "px-6 py-2 text-xs",
        md: "px-8 py-3 text-sm",
        lg: "px-10 py-4 text-base",
        xl: "px-12 py-5 text-lg",
      },
    },
    defaultVariants: {
      size: "md",
    },
  },
)

export interface FlowButtonProps extends VariantProps<typeof buttonVariants> {
  /** Button text content */
  text?: string
  /** Optional CSS class name for additional styling */
  className?: string
  /** Custom width (e.g., "200px", "10rem", etc.) */
  width?: string
  /** Custom height (e.g., "50px", "3rem", etc.) */
  height?: string
  /** Optional click handler */
  onClick?: () => void
  /** Optional array of colors for the glow effect */
  glowColors?: string[]
}

export function FlowButton({
  text = "Modern Button",
  size,
  className,
  width,
  height,
  onClick,
  glowColors = ["#667eea", "#764ba2", "#f093fb", "#f5576c"],
}: FlowButtonProps) {
  // Calculate arrow and circle sizes based on button size
  const getArrowSize = () => {
    if (size === "sm") return "w-3 h-3"
    if (size === "lg") return "w-5 h-5"
    if (size === "xl") return "w-6 h-6"
    return "w-4 h-4" // Default (md)
  }

  const getCircleSize = () => {
    if (size === "sm") return "group-hover:w-[180px] group-hover:h-[180px]"
    if (size === "lg") return "group-hover:w-[260px] group-hover:h-[260px]"
    if (size === "xl") return "group-hover:w-[300px] group-hover:h-[300px]"
    return "group-hover:w-[220px] group-hover:h-[220px]" // Default (md)
  }

  // Custom style for width and height if provided
  const customStyle: React.CSSProperties = {}
  if (width) customStyle.width = width
  if (height) customStyle.height = height

  // Adjust glow effect size based on button size
  const getGlowInset = () => {
    if (size === "sm") return "inset-[-6px]"
    if (size === "lg") return "inset-[-10px]"
    if (size === "xl") return "inset-[-12px]"
    return "inset-[-8px]" // Default (md)
  }

  const getFallbackGlowInset = () => {
    if (size === "sm") return "inset-[-3px]"
    if (size === "lg") return "inset-[-5px]"
    if (size === "xl") return "inset-[-6px]"
    return "inset-[-4px]" // Default (md)
  }

  return (
    <div className="group relative inline-block">
      {/* Single Adaptive Glow Effect */}
      <div
        className={cn(
          "absolute opacity-40 group-hover:opacity-60 group-hover:scale-100 transition-all duration-[600ms] ease-[cubic-bezier(0.23,1,0.32,1)]",
          getGlowInset(),
        )}
      >
        <GlowEffect
          colors={glowColors}
          mode="rotate"
          blur="soft"
          duration={4}
          scale={1}
          className="rounded-[50px] group-hover:rounded-[12px]"
        />
      </div>

      {/* Fallback CSS Glow for better compatibility */}
      <div
        className={cn(
          "absolute opacity-30 group-hover:opacity-70 rounded-[100px] group-hover:rounded-[12px] transition-all duration-[600ms] ease-[cubic-bezier(0.23,1,0.32,1)] bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 blur-sm",
          getFallbackGlowInset(),
        )}
      ></div>

      <button className={cn(buttonVariants({ size }), className)} style={customStyle} onClick={onClick}>
        {/* Left arrow (arr-2) */}
        <ArrowRight
          className={cn(
            "absolute left-[-25%] stroke-[#111111] fill-none z-[9] group-hover:left-4 group-hover:stroke-black transition-all duration-[800ms] ease-[cubic-bezier(0.34,1.56,0.64,1)]",
            getArrowSize(),
          )}
        />

        {/* Text */}
        <span className="relative z-[1] -translate-x-3 group-hover:translate-x-3 transition-all duration-[800ms] ease-out">
          {text}
        </span>

        {/* Circle - Using gray-200/80 as requested */}
        <span
          className={cn(
            "absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-white/90  rounded-[50%] opacity-0 group-hover:opacity-0 transition-all duration-[800ms] ease-[cubic-bezier(0.19,1,0.22,1)]",
            getCircleSize(),
          )}
        ></span>

        {/* Right arrow (arr-1) */}
        <ArrowRight
          className={cn(
            "absolute right-4 stroke-[#111111] fill-none z-[9] group-hover:right-[-25%] group-hover:stroke-black transition-all duration-[800ms] ease-[cubic-bezier(0.34,1.56,0.64,1)]",
            getArrowSize(),
          )}
        />
      </button>
    </div>
  )
}
