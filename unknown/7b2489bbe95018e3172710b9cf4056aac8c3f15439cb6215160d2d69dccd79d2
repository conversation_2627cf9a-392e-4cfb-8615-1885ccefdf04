import type { PricingTier } from "@/types/pricing"

// Example: Basic pricing data
export const basicPricingTiers: PricingTier[] = [
  {
    id: "free",
    name: "Individuals",
    price: "Free",
    description: "For your hobby projects",
    features: [
      { name: "Email alerts", included: true, value: "Free email alerts" },
      { name: "Check frequency", included: true, value: "3-minute checks" },
      { name: "Data enrichment", included: true, value: "Automatic data enrichment" },
      { name: "Monitors", included: true, value: "10 monitors" },
      { name: "Team seats", included: true, value: "Up to 3 seats" },
      { name: "Phone calls", included: false },
      { name: "User accounts", included: false },
    ],
    buttonText: "Get started",
    buttonVariant: "default",
  },
  {
    id: "teams",
    name: "Teams",
    price: 90,
    period: "month/user",
    description: "Great for small businesses",
    features: [
      { name: "Email alerts", included: true, value: "Premium email alerts" },
      { name: "Check frequency", included: true, value: "30 second checks" },
      { name: "Data enrichment", included: true, value: "Advanced data enrichment" },
      { name: "Monitors", included: true, value: "20 monitors" },
      { name: "Team seats", included: true, value: "Up to 6 seats" },
      { name: "Phone calls", included: true, value: "Unlimited phone calls" },
      { name: "User accounts", included: true, value: "Single-user account" },
    ],
    popular: true,
    buttonText: "Get started",
    buttonVariant: "primary",
    badge: {
      text: "Most Popular",
      icon: "🔥",
      color: "orange",
    },
  },
  {
    id: "organizations",
    name: "Organizations",
    price: 120,
    period: "month/user",
    description: "Great for large businesses",
    features: [
      { name: "Email alerts", included: true, value: "Enterprise email alerts" },
      { name: "Check frequency", included: true, value: "15 second checks" },
      { name: "Data enrichment", included: true, value: "Enterprise data enrichment" },
      { name: "Monitors", included: true, value: "50 monitors" },
      { name: "Team seats", included: true, value: "Up to 10 seats" },
      { name: "Phone calls", included: true, value: "Unlimited phone calls" },
      { name: "User accounts", included: true, value: "Multi-user accounts" },
    ],
    buttonText: "Get started",
    buttonVariant: "default",
  },
]

// Example: SaaS pricing data
export const saasPricingTiers: PricingTier[] = [
  {
    id: "starter",
    name: "Starter",
    price: 29,
    period: "month",
    description: "Perfect for small teams",
    features: [
      { name: "Projects", included: true, value: "5 projects" },
      { name: "Storage", included: true, value: "10GB storage" },
      { name: "Users", included: true, value: "Up to 3 users" },
      { name: "Support", included: true, value: "Email support" },
      { name: "API access", included: false },
      { name: "Advanced analytics", included: false },
    ],
    buttonText: "Start free trial",
    buttonVariant: "default",
  },
  {
    id: "professional",
    name: "Professional",
    price: 99,
    period: "month",
    description: "For growing businesses",
    features: [
      { name: "Projects", included: true, value: "Unlimited projects" },
      { name: "Storage", included: true, value: "100GB storage" },
      { name: "Users", included: true, value: "Up to 10 users" },
      { name: "Support", included: true, value: "Priority support" },
      { name: "API access", included: true, value: "Full API access" },
      { name: "Advanced analytics", included: true },
    ],
    popular: true,
    buttonText: "Start free trial",
    buttonVariant: "primary",
    badge: {
      text: "Most Popular",
      icon: "⭐",
      color: "blue",
    },
  },
  {
    id: "enterprise",
    name: "Enterprise",
    price: "Custom",
    description: "For large organizations",
    features: [
      { name: "Projects", included: true, value: "Unlimited projects" },
      { name: "Storage", included: true, value: "Unlimited storage" },
      { name: "Users", included: true, value: "Unlimited users" },
      { name: "Support", included: true, value: "24/7 dedicated support" },
      { name: "API access", included: true, value: "Full API access" },
      { name: "Advanced analytics", included: true },
      { name: "Custom integrations", included: true },
      { name: "SLA guarantee", included: true },
    ],
    buttonText: "Contact sales",
    buttonVariant: "default",
  },
]
