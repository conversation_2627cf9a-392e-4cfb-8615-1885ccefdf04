 

"use client";

import React, { useRef, useState, useEffect, useCallback } from 'react';
import { VideoHandler, type VideoHandlerRef } from './video-handler/VideoHandler';
import { VideoViewPanel } from './video-view-panel/VideoViewPanel';
import { videoPresets, type VideoPreset, defaultPreset } from './video-presets';

type Theme = 'light' | 'dark';

export const VideoAsciiPanel: React.FC = () => {
    const refVideoHandler = useRef<VideoHandlerRef>(null);
    const videoRef = useRef<HTMLVideoElement>(null);
    const attemptAutoplayRef = useRef(false); // Ref to signal autoplay intent

    const [isVideoReady, setIsVideoReady] = useState(false);
    // Initialize with defaultPreset values
    const [charsPerLine, setCharsPerLine] = useState(defaultPreset.charsPerLine);
    const [charsPerColumn, setCharsPerColumn] = useState(defaultPreset.charsPerColumn ?? 0); // Ensure default can also be 0 for auto-calc
    const [currentCharacterSize, setCurrentCharacterSize] = useState<number>(defaultPreset.characterSize ?? 10);

    const [selectedPresetName, setSelectedPresetName] = useState<string>(defaultPreset.name);

    const [isPresetView, setIsPresetView] = useState(true); // Start in preset view
    const [currentTheme, setCurrentTheme] = useState<Theme>('dark'); // Theme state

    const loadPreset = useCallback((preset: VideoPreset) => {
        setCharsPerLine(preset.charsPerLine);
        setCharsPerColumn(preset.charsPerColumn ?? 0); // Use 0 to signal auto-calc if undefined
        setCurrentCharacterSize(preset.characterSize ?? 10); // Update character size
        
        if (videoRef.current) {
            videoRef.current.src = preset.remoteUrl;
            // videoRef.current.loop = preset.loop; // Removed: VideoHandler now controls looping via onended
            videoRef.current.controls = false;
            videoRef.current.muted = true; // Mute for better autoplay chances
            videoRef.current.load();
            attemptAutoplayRef.current = true; // Signal that we want to play this on canplay
        }
        setIsVideoReady(false); // onCanPlayCallback will set this to true and attempt play
        setIsPresetView(true);
        setSelectedPresetName(preset.name);
    }, [videoRef]);

    // Load default preset on initial mount
    useEffect(() => {
        const initialPreset = videoPresets.find(p => p.name === defaultPreset.name) || defaultPreset;
        if (initialPreset) {
             loadPreset(initialPreset);
        }
    }, [loadPreset]);

    const handlePresetChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
        const newPresetName = event.target.value;
        const preset = videoPresets.find(p => p.name === newPresetName);
        if (preset) {
            loadPreset(preset);
        }
    };

    const onCanPlayCallback = useCallback(() => {
        setIsVideoReady(true);
        if (videoRef.current && attemptAutoplayRef.current) {
            videoRef.current.play().catch(error => {
                console.error("Error attempting to autoplay video in onCanPlay:", error);
            });
            attemptAutoplayRef.current = false; // Reset the flag
        }
    }, [videoRef]);


    const onEjectVideo = useCallback(() => {
        setIsVideoReady(false);
        setIsPresetView(false); // Switch to manual mode
        if (videoRef.current) {
            videoRef.current.src = '';
            videoRef.current.pause();
        }
        refVideoHandler.current?.ejectVideo();
        
        setCharsPerLine(100); 
        setCharsPerColumn(0); // Let VideoHandler calculate for manual mode
        setSelectedPresetName(''); // No preset selected
        setCurrentCharacterSize(10); // Reset to default for manual mode
    }, [videoRef]);

    const handleManualFileSelect = useCallback(() => {
        setIsPresetView(false);
        setSelectedPresetName('');
    }, []);

    const toggleTheme = () => {
        setCurrentTheme(prevTheme => (prevTheme === 'dark' ? 'light' : 'dark'));
    };

    return (
        <div>
            <div style={{ marginBottom: '1rem' }}>
                <label htmlFor="preset-select" style={{ marginRight: '10px' }}>Select Preset:</label>
                <select id="preset-select" value={selectedPresetName} onChange={handlePresetChange} style={{ marginRight: '10px' }}>
                    {videoPresets.map(preset => (
                        <option key={preset.name} value={preset.name}>
                            {preset.name}
                        </option>
                    ))}
                </select>
                {isPresetView && (
                    <button onClick={onEjectVideo} title="Stop preset and select file manually" style={{ marginRight: '10px' }}>Switch to Manual Upload</button>
                )}
                <button onClick={toggleTheme}>
                    Switch to {currentTheme === 'dark' ? 'Light' : 'Dark'} Mode
                </button>
            </div>

            <VideoHandler
                videoRef={videoRef}
                onCanPlay={onCanPlayCallback}
                ref={refVideoHandler}
                
                setCharsPerLine={setCharsPerLine}
                setCharsPerColumn={setCharsPerColumn}
                
                isPresetActive={isPresetView}
                initialCharsPerLine={isPresetView ? charsPerLine : undefined}
                initialCharsPerColumn={isPresetView ? charsPerColumn : undefined}
                onManualFileSelected={handleManualFileSelect}
            />

            {isVideoReady && videoRef.current && (
                <VideoViewPanel
                    videoRef={videoRef.current}
                    charsPerLine={charsPerLine}
                    charsPerColumn={charsPerColumn}
                    onEjectVideo={onEjectVideo}
                    hideControls={isPresetView} // Pass isPresetView to hide controls
                    fontSize={currentCharacterSize} // Pass character size
                    theme={currentTheme} // Pass current theme
                />
            )}
        </div>
    );
};
