'use client';

import { useEffect, useRef } from 'react';
import Matter from 'matter-js';

const KineticScene = () => {
  const sceneRef = useRef<HTMLDivElement>(null);
  const engineRef = useRef<Matter.Engine | null>(null);

  useEffect(() => {
    if (!sceneRef.current) return;

    const engine = Matter.Engine.create();
    engineRef.current = engine;
    const world = engine.world;

    const render = Matter.Render.create({
      element: sceneRef.current,
      engine: engine,
      options: {
        width: window.innerWidth,
        height: window.innerHeight,
        wireframes: false,
        background: '#f0f0f0',
      },
    });

    // Ground
    const ground = Matter.Bodies.rectangle(
      window.innerWidth / 2,
      window.innerHeight + 25,
      window.innerWidth,
      50,
      { isStatic: true, render: { fillStyle: 'grey' } }
    );
    Matter.World.add(world, ground);

    // Walls
    const leftWall = Matter.Bodies.rectangle(-25, window.innerHeight / 2, 50, window.innerHeight, { isStatic: true });
    const rightWall = Matter.Bodies.rectangle(window.innerWidth + 25, window.innerHeight / 2, 50, window.innerHeight, { isStatic: true });
    Matter.World.add(world, [leftWall, rightWall]);

    // Text bodies
    const words = ['Hello', 'World', 'Matter.js', 'Next.js', 'Kinetic', 'Typography', 'Falling', 'Text'];
    const bodies: Matter.Body[] = [];

    words.forEach(word => {
        const body = Matter.Bodies.rectangle(
            Math.random() * window.innerWidth,
            Math.random() * -500, // Start above the screen
            word.length * 20, // Approximate width based on word length
            40, // Height
            {
                restitution: 0.5,
                render: {
                    sprite: {
                        texture: createTextTexture(word),
                        xScale: 1,
                        yScale: 1
                    }
                }
            }
        );
        bodies.push(body);
    });

    Matter.World.add(world, bodies);

    // Mouse control
    const mouse = Matter.Mouse.create(render.canvas);
    const mouseConstraint = Matter.MouseConstraint.create(engine, {
      mouse: mouse,
      constraint: {
        stiffness: 0.2,
        render: {
          visible: false,
        },
      },
    });

    Matter.World.add(world, mouseConstraint);

    // Run the engine and renderer
    Matter.Engine.run(engine);
    Matter.Render.run(render);

    // Handle window resize
    const handleResize = () => {
        if (render && sceneRef.current) {
            render.canvas.width = window.innerWidth;
            render.canvas.height = window.innerHeight;
            Matter.Body.setPosition(ground, { x: window.innerWidth / 2, y: window.innerHeight + 25 });
            Matter.Body.setPosition(leftWall, { x: -25, y: window.innerHeight / 2 });
            Matter.Body.setPosition(rightWall, { x: window.innerWidth + 25, y: window.innerHeight / 2 });
        }
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      if (engineRef.current) {
        Matter.Render.stop(render);
        Matter.Engine.clear(engineRef.current);
        render.canvas.remove();
        render.textures = {};
      }
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const createTextTexture = (text: string) => {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (context) {
        const fontSize = 40;
        context.font = `${fontSize}px Arial`;
        const textMetrics = context.measureText(text);
        canvas.width = textMetrics.width;
        canvas.height = fontSize;
        context.font = `${fontSize}px Arial`;
        context.fillStyle = '#000000';
        context.fillText(text, 0, fontSize - 10);
    }
    return canvas.toDataURL();
  }

  return <div ref={sceneRef} style={{ width: '100vw', height: '100vh', overflow: 'hidden' }} />;
};

export default KineticScene;
