export interface PricingFeature {
  name: string
  included: boolean
  value?: string | number
  description?: string
}

export interface PricingTier {
  id: string
  name: string
  price: string | number
  period?: string
  description: string
  features: PricingFeature[]
  popular?: boolean
  buttonText?: string
  buttonVariant?: "default" | "primary"
  badge?: {
    text: string
    icon?: string
    color?: string
  }
}

export interface PricingComponentProps {
  className?: string
  tiers?: PricingTier[]
  currency?: string
  showToggle?: boolean
}
