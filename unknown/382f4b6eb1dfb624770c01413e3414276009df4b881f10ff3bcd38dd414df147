import React, { useRef, useEffect, useMemo, useState } from 'react';
import * as THREE from 'three';
import { useFrame } from '@react-three/fiber';
import { ExtrudedImageOptions } from './types';
import { GifReader } from 'omggif';

const brightnessVertexShader = `
varying vec2 vUv;
void main() {
  vUv = uv;
  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
}
`;

const brightnessFragmentShader = `
uniform sampler2D uTexture;
uniform float uBrightness;
uniform float uAlphaTest;
varying vec2 vUv;
void main() {
  vec4 texColor = texture2D(uTexture, vUv);
  if (texColor.a < uAlphaTest) {
    discard;
  }
  vec3 finalColor;
  if (uBrightness <= 1.0) {
    finalColor = texColor.rgb * uBrightness;
  } else {
    float enhancement = uBrightness - 1.0;
    finalColor = texColor.rgb + vec3(enhancement);
  }
  finalColor = clamp(finalColor, 0.0, 1.0);
  gl_FragColor = vec4(finalColor, texColor.a);
}
`;

interface ExtrudedImageMeshProps {
  options: ExtrudedImageOptions;
  imageSrc?: string; // URL for static images
  gifBuffer?: ArrayBuffer; // Buffer for GIF files
  sideColor?: string; // Color for the extruded side faces
  onDimensionsChange?: (dimensions: { width: number; height: number }) => void; // Callback for when image dimensions are known
}

// --- Helper Functions (adapted from extruder.ts) ---
const loadImageElement = (src: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'Anonymous'; // Important for canvas operations if image is from another domain
    img.onload = () => resolve(img);
    img.onerror = (err) => reject(err);
    img.src = src;
  });
};

const getImageDataFromImage = (img: HTMLImageElement, pixelationFactor: number = 1): ImageData => {
  const targetWidth = pixelationFactor > 1 ? Math.max(1, Math.floor(img.width / pixelationFactor)) : img.width;
  const targetHeight = pixelationFactor > 1 ? Math.max(1, Math.floor(img.height / pixelationFactor)) : img.height;

  const canvas = document.createElement('canvas');
  canvas.width = targetWidth;
  canvas.height = targetHeight;
  const ctx = canvas.getContext('2d', { willReadFrequently: true });
  if (!ctx) throw new Error('Could not get 2D context');
  
  // When pixelating, we draw the full image onto the smaller canvas, letting the browser do the scaling.
  ctx.drawImage(img, 0, 0, targetWidth, targetHeight);
  return ctx.getImageData(0, 0, targetWidth, targetHeight);
};

const createTextureFromImageData = (imageData: ImageData): THREE.Texture => {
  const texture = new THREE.DataTexture(imageData.data, imageData.width, imageData.height, THREE.RGBAFormat);
  texture.flipY = false; // Consistent with original behavior
  texture.colorSpace = THREE.SRGBColorSpace;
  texture.minFilter = THREE.NearestFilter;
  texture.magFilter = THREE.NearestFilter;
  texture.needsUpdate = true;
  return texture;
};

// Full implementation for geometry generation from image data
const createGeometryFromImageDataInternal = (imageData: ImageData, opts: ExtrudedImageOptions): THREE.BufferGeometry => {
  const { width, height, data } = imageData;
  const { alphaThreshold, thickness: depth, blackThreshold = 255 } = opts; // Default blackThreshold if not provided

  const frontBackVertices: number[] = [];
  const frontBackNormals: number[] = [];
  const frontBackUVs: number[] = [];

  const sideVertices: number[] = [];
  const sideNormals: number[] = [];
  const sideUVs: number[] = []; // Side UVs can be simple, e.g., all (0,0)

  const isSolid = (x: number, y: number): boolean => {
    if (x < 0 || x >= width || y < 0 || y >= height) return false;
    const idx = (y * width + x) * 4;
    const r = data[idx];
    const g = data[idx + 1];
    const b = data[idx + 2];
    const a = data[idx + 3];
    if (a < alphaThreshold) return false;
    // If blackThreshold is 255, this check effectively does nothing (allows all colors).
    // If blackThreshold is lower, it filters out pixels that are too light.
    if (r > blackThreshold || g > blackThreshold || b > blackThreshold) return false;
    return true;
  };

  // Helper to add a quad (two triangles) to the specified geometry arrays
  const addFaceToGroup = (
    targetVertices: number[], targetNormals: number[], targetUVs: number[],
    v1: THREE.Vector3, v2: THREE.Vector3, v3: THREE.Vector3, v4: THREE.Vector3,
    normalVec: THREE.Vector3,
    uv_v1: THREE.Vector2, uv_v2: THREE.Vector2, uv_v3: THREE.Vector2, uv_v4: THREE.Vector2
  ) => {
    // Triangle 1: v1, v2, v3
    targetVertices.push(v1.x, v1.y, v1.z, v2.x, v2.y, v2.z, v3.x, v3.y, v3.z);
    targetNormals.push(normalVec.x, normalVec.y, normalVec.z, normalVec.x, normalVec.y, normalVec.z, normalVec.x, normalVec.y, normalVec.z);
    targetUVs.push(uv_v1.x, uv_v1.y, uv_v2.x, uv_v2.y, uv_v3.x, uv_v3.y);

    // Triangle 2: v1, v3, v4
    targetVertices.push(v1.x, v1.y, v1.z, v3.x, v3.y, v3.z, v4.x, v4.y, v4.z);
    targetNormals.push(normalVec.x, normalVec.y, normalVec.z, normalVec.x, normalVec.y, normalVec.z, normalVec.x, normalVec.y, normalVec.z);
    targetUVs.push(uv_v1.x, uv_v1.y, uv_v3.x, uv_v3.y, uv_v4.x, uv_v4.y);
  };

  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      if (isSolid(x, y)) {
        const px0 = x / width - 0.5;
        const py0 = 0.5 - y / height;
        const px1 = (x + 1) / width - 0.5;
        const py1 = 0.5 - (y + 1) / height;

        const u0 = x / width;
        const v0_tex = y / height;
        const u1 = (x + 1) / width;
        const v1_tex = (y + 1) / height;

        const halfDepth = depth / 2;

        // Front face (+Z) - Material Group 0
        addFaceToGroup(
          frontBackVertices, frontBackNormals, frontBackUVs,
          new THREE.Vector3(px0, py0, halfDepth), new THREE.Vector3(px1, py0, halfDepth),
          new THREE.Vector3(px1, py1, halfDepth), new THREE.Vector3(px0, py1, halfDepth),
          new THREE.Vector3(0, 0, 1),
          new THREE.Vector2(u0, v0_tex), new THREE.Vector2(u1, v0_tex),
          new THREE.Vector2(u1, v1_tex), new THREE.Vector2(u0, v1_tex)
        );

        // Back face (-Z) - Material Group 0
        addFaceToGroup(
          frontBackVertices, frontBackNormals, frontBackUVs,
          new THREE.Vector3(px0, py0, -halfDepth), new THREE.Vector3(px0, py1, -halfDepth),
          new THREE.Vector3(px1, py1, -halfDepth), new THREE.Vector3(px1, py0, -halfDepth),
          new THREE.Vector3(0, 0, -1),
          new THREE.Vector2(u0, v0_tex), new THREE.Vector2(u0, v1_tex),
          new THREE.Vector2(u1, v1_tex), new THREE.Vector2(u1, v0_tex)
        );

        // Side UVs (can be simple for solid color)
        const sideUV_00 = new THREE.Vector2(0, 0);
        const sideUV_10 = new THREE.Vector2(1, 0);
        const sideUV_11 = new THREE.Vector2(1, 1);
        const sideUV_01 = new THREE.Vector2(0, 1);

        // Top face (side, +Y) - Material Group 1
        if (!isSolid(x, y - 1)) {
          addFaceToGroup(sideVertices, sideNormals, sideUVs,
            new THREE.Vector3(px0, py0, -halfDepth), new THREE.Vector3(px0, py0, halfDepth),
            new THREE.Vector3(px1, py0, halfDepth), new THREE.Vector3(px1, py0, -halfDepth),
            new THREE.Vector3(0, 1, 0),
            sideUV_00, sideUV_01, sideUV_11, sideUV_10 // Adjusted for typical quad UV mapping
          );
        }

        // Bottom face (side, -Y) - Material Group 1
        if (!isSolid(x, y + 1)) {
          addFaceToGroup(sideVertices, sideNormals, sideUVs,
            new THREE.Vector3(px0, py1, halfDepth), new THREE.Vector3(px0, py1, -halfDepth),
            new THREE.Vector3(px1, py1, -halfDepth), new THREE.Vector3(px1, py1, halfDepth),
            new THREE.Vector3(0, -1, 0),
            sideUV_00, sideUV_01, sideUV_11, sideUV_10
          );
        }

        // Left face (side, -X) - Material Group 1
        if (!isSolid(x - 1, y)) {
          addFaceToGroup(sideVertices, sideNormals, sideUVs,
            new THREE.Vector3(px0, py0, halfDepth), new THREE.Vector3(px0, py0, -halfDepth),
            new THREE.Vector3(px0, py1, -halfDepth), new THREE.Vector3(px0, py1, halfDepth),
            new THREE.Vector3(-1, 0, 0),
            sideUV_00, sideUV_01, sideUV_11, sideUV_10
          );
        }

        // Right face (side, +X) - Material Group 1
        if (!isSolid(x + 1, y)) {
          addFaceToGroup(sideVertices, sideNormals, sideUVs,
            new THREE.Vector3(px1, py0, -halfDepth), new THREE.Vector3(px1, py0, halfDepth),
            new THREE.Vector3(px1, py1, halfDepth), new THREE.Vector3(px1, py1, -halfDepth),
            new THREE.Vector3(1, 0, 0),
            sideUV_00, sideUV_01, sideUV_11, sideUV_10
          );
        }
      }
    }
  }

  const geometry = new THREE.BufferGeometry();
  const numFrontBackVerts = frontBackVertices.length / 3;
  const numSideVerts = sideVertices.length / 3;

  if (numFrontBackVerts > 0 || numSideVerts > 0) {
    const finalVertices = frontBackVertices.concat(sideVertices);
    const finalNormals = frontBackNormals.concat(sideNormals);
    const finalUVs = frontBackUVs.concat(sideUVs);

    geometry.setAttribute('position', new THREE.Float32BufferAttribute(finalVertices, 3));
    geometry.setAttribute('normal', new THREE.Float32BufferAttribute(finalNormals, 3));
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(finalUVs, 2));

    if (numFrontBackVerts > 0) {
      geometry.addGroup(0, numFrontBackVerts, 0); // Material index 0 for front/back
    }
    if (numSideVerts > 0) {
      geometry.addGroup(numFrontBackVerts, numSideVerts, 1); // Material index 1 for sides
    }
    geometry.computeBoundingSphere();
  }
  return geometry;
};

// Full implementation for legacy geometry generation

const createLegacyGeometryInternal = (imageData: ImageData, opts: ExtrudedImageOptions): THREE.BufferGeometry => {
  const { width: imgWidth, height: imgHeight, data: imgData } = imageData;
  const { alphaThreshold, thickness, blackThreshold = 255 } = opts; // Default blackThreshold, sizeX/Y for mesh.scale

  const isSolidPixel = (x: number, y: number): boolean => {
    if (x < 0 || x >= imgWidth || y < 0 || y >= imgHeight) return false;
    const idx = (y * imgWidth + x) * 4;
    const r = imgData[idx];
    const g = imgData[idx + 1];
    const b = imgData[idx + 2];
    const a = imgData[idx + 3];
    if (a < alphaThreshold) return false;
    if (r > blackThreshold || g > blackThreshold || b > blackThreshold) return false;
    return true;
  };

  const traceOutlineInternal = (): { outline: [number, number][]; bounds: { minX: number; minY: number; maxX: number; maxY: number } } | null => {
    let startX = -1, startY = -1;
    const outline: [number, number][] = [];
    const bounds = { minX: imgWidth, minY: imgHeight, maxX: 0, maxY: 0 };

    // Find first solid pixel to start tracing
    for (let y = 0; y < imgHeight; y++) {
      for (let x = 0; x < imgWidth; x++) {
        if (isSolidPixel(x, y)) {
          startX = x;
          startY = y;
          break;
        }
      }
      if (startX !== -1) break;
    }

    if (startX === -1) return null; // No solid pixels found

    const updateBounds = (x: number, y: number) => {
      bounds.minX = Math.min(bounds.minX, x);
      bounds.minY = Math.min(bounds.minY, y);
      bounds.maxX = Math.max(bounds.maxX, x);
      bounds.maxY = Math.max(bounds.maxY, y);
    };

    const isEdgePixel = (x: number, y: number): boolean => {
      if (!isSolidPixel(x, y)) return false;
      // Check 8 neighbors
      for (let dy = -1; dy <= 1; dy++) {
        for (let dx = -1; dx <= 1; dx++) {
          if (dx === 0 && dy === 0) continue;
          if (!isSolidPixel(x + dx, y + dy)) return true; // Neighbor is not solid, so (x,y) is an edge
        }
      }
      return false;
    };
    
    // Find a starting edge pixel for robust tracing
    let foundStartEdge = false;
    for (let y = 0; y < imgHeight; y++) {
        for (let x = 0; x < imgWidth; x++) {
            if (isEdgePixel(x,y)) {
                startX = x;
                startY = y;
                foundStartEdge = true;
                break;
            }
        }
        if (foundStartEdge) break;
    }

    if (!foundStartEdge) return null; // No edge pixels found, might be a fully solid image with no holes to trace an outer edge

    let currentX = startX;
    let currentY = startY;
    let currentDir = 0; // 0: right, 1: down, 2: left, 3: up (initial direction to check)
    const directions = [[1, 0], [0, 1], [-1, 0], [0, -1]]; // dx, dy

    do {
      outline.push([currentX, currentY]);
      updateBounds(currentX, currentY);

      // Moore neighborhood tracing logic (simplified)
      // Try to turn left relative to current direction, then straight, then right
      let foundNext = false;
      for (let i = 0; i < 4; i++) {
        const nextDir = (currentDir + 3 + i) % 4; // +3 for left, +0 for straight, +1 for right
        const nextX = currentX + directions[nextDir][0];
        const nextY = currentY + directions[nextDir][1];

        if (isSolidPixel(nextX, nextY)) {
            // Check if this pixel is an edge pixel or if moving to it makes sense
            // This simplified version might not handle all complex shapes or holes perfectly
            // but aims for the outer contour.
            // A more robust check involves ensuring the pixel to the 'right' of the new direction is empty.
            const rightOfNextDirX = nextX + directions[(nextDir + 1) % 4][0];
            const rightOfNextDirY = nextY + directions[(nextDir + 1) % 4][1];

            if (isEdgePixel(nextX, nextY) || !isSolidPixel(rightOfNextDirX, rightOfNextDirY)) {
                currentX = nextX;
                currentY = nextY;
                currentDir = nextDir;
                foundNext = true;
                break;
            }
        }
      }
      if (!foundNext) {
        // console.warn('Legacy outline trace stuck or completed unexpectedly.');
        break; // Should not happen if start is valid edge and shape is simple
      }
    } while ((currentX !== startX || currentY !== startY) && outline.length < imgWidth * imgHeight * 2); // Safety break

    if (outline.length === 0) return null;
    return { outline, bounds };
  };

  const setUVsForExtrudeGeometryInternal = (
    geometry: THREE.ExtrudeGeometry,
    shapeBounds: { minX: number; minY: number; maxX: number; maxY: number }
  ) => {
    geometry.computeBoundingBox();
    const geomBbox = geometry.boundingBox;
    if (!geomBbox) return;

    const uvs = geometry.attributes.uv;
    const positions = geometry.attributes.position;
    const shapeWidth = shapeBounds.maxX - shapeBounds.minX + 1; // +1 because bounds are inclusive pixel indices
    const shapeHeight = shapeBounds.maxY - shapeBounds.minY + 1;

    for (let i = 0; i < positions.count; i++) {
      const x = positions.getX(i);
      const y = positions.getY(i);
      const z = positions.getZ(i);

      let u, v;

      // Front and back faces (Z is close to 0 or opts.thickness)
      // The ExtrudeGeometry positions are relative to the shape's center after it's centered by THREE.ShapeUtils.centerPoints
      // So we need to map them back to the original image pixel space for UVs.
      if (Math.abs(z) < 0.001 || Math.abs(z - thickness) < 0.001) {
        // UVs for front/back faces should map the original pixel coordinates within the traced bounds
        // This requires knowing how the shape vertices map to the geometry vertices.
        // The x,y from geometry are scaled by 'size' and centered.
        // We need to reverse this to get to the 0-1 range of the *shape* before extrusion.
        // Since the shape is now generated at 1:1 scale and scaling is handled by mesh.scale,
        // this custom UV logic might need re-evaluation or can rely on default ExtrudeGeometry UVs.
        // Commenting out the problematic lines that used 'size'.
        // const normalizedShapeX = x + 0.5; // If size was 1.0 and x was unscaled shape coord
        // const normalizedShapeY = y + 0.5; // If size was 1.0 and y was unscaled shape coord
        // u = (shapeBounds.minX / imgWidth) + (normalizedShapeX * shapeWidth / imgWidth);
        // v = (shapeBounds.minY / imgHeight) + (normalizedShapeY * shapeHeight / imgHeight);
        // The default ExtrudeGeometry UVs for front/back are often okay if the shape is simple and normalized 0-1.
        // However, for precise mapping from original image, this calculation is needed.
        // For simplicity here, we'll rely on the default UVs for front/back and adjust if needed.
        // The default UVs for ExtrudeGeometry are based on the 2D shape's vertices.
        // Calculate UVs based on the vertex position within the geometry's own bounding box,
        // then map this to the sub-region of the original image defined by shapeBounds.
        const u_geom_norm = (x - geomBbox.min.x) / (geomBbox.max.x - geomBbox.min.x);
        const v_geom_norm = (y - geomBbox.min.y) / (geomBbox.max.y - geomBbox.min.y); // V=0 at bottom of geometry

        u = (shapeBounds.minX / imgWidth) + (u_geom_norm * shapeWidth / imgWidth);
        // Since texture V=0 is top (texture.flipY = false), and geometry V=0 is bottom, use (1.0 - v_geom_norm)
        v = (shapeBounds.minY / imgHeight) + ((1.0 - v_geom_norm) * shapeHeight / imgHeight);

      } else {
        // Side faces - this is tricky. A common approach is cylindrical-like mapping or just stretching a texture.
        // For this port, let's use a simple stretch based on normalized Z and perimeter length (approx).
        // This part is highly dependent on desired visual outcome for sides.
        // The original extruder.ts side UVs were basic (0,0 to 1,1), which we used for the pixel-by-pixel method.
        // For ExtrudeGeometry, it's more complex.
        // Let's try to map U based on angle (like cylindrical) and V based on depth.
        const angle = Math.atan2(y, x); // Angle around Z axis
        u = (angle + Math.PI) / (2 * Math.PI); // Normalize angle to 0-1
        v = z / thickness; // Normalize depth to 0-1
      }
      uvs.setXY(i, u, v);
    }
    uvs.needsUpdate = true;
  };

  const outlineData = traceOutlineInternal();
  if (!outlineData || outlineData.outline.length < 3) {
    // console.warn('Legacy trace failed or outline too short.');
    return new THREE.BufferGeometry(); // Return empty if trace fails
  }

  const { outline, bounds: shapeBounds } = outlineData;
  const shape = new THREE.Shape();

  // Convert outline points to THREE.Vector2 for the shape
  // Normalize points to be centered around (0,0) and scaled by 'size'
  const shapeWidth = shapeBounds.maxX - shapeBounds.minX + 1;
  const shapeHeight = shapeBounds.maxY - shapeBounds.minY + 1;

  outline.forEach(([px, py], i) => {
    // Normalize pixel coordinates relative to its own bounding box, then scale and center
    const normalizedX = (px - shapeBounds.minX) / shapeWidth;
    const normalizedY = (py - shapeBounds.minY) / shapeHeight;
    // Generate shape at 1:1 scale; overall scaling is handled by mesh.scale(sizeX, sizeY, 1)
    const xPos = (normalizedX - 0.5) * shapeWidth; // Scale by shapeWidth to maintain aspect ratio before mesh scaling
    const yPos = (0.5 - normalizedY) * shapeHeight; // Scale by shapeHeight, Y inverted for 3D

    if (i === 0) {
      shape.moveTo(xPos, yPos);
    } else {
      shape.lineTo(xPos, yPos);
    }
  });

  const extrudeSettings = {
    steps: 1,
    depth: thickness,
    bevelEnabled: false,
  };

  let geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
  
  // The UVs generated by ExtrudeGeometry might need adjustment, especially for the sides
  // and to ensure correct mapping from the original image sprite within its traced bounds.
  setUVsForExtrudeGeometryInternal(geometry, shapeBounds);

  return geometry;
};

const ExtrudedImageMesh: React.FC<ExtrudedImageMeshProps> = ({ options, imageSrc: initialImageSrc, gifBuffer: initialGifBuffer, sideColor = '#000000', onDimensionsChange }) => {
  const meshRef = useRef<THREE.Mesh>(null!);
  const geometryRef = useRef<THREE.BufferGeometry | null>(null);
  const clockRef = useRef(new THREE.Clock());
  const lastFrameChangeTimeRef = useRef(0);

  const frontBackMaterial = useMemo(() => {
    const material = new THREE.ShaderMaterial({
      uniforms: {
        uTexture: { value: null }, // Will be set in useEffect
        uBrightness: { value: options.brightness ?? 1.0 },
        uAlphaTest: { value: (options.alphaThreshold ?? 128) / 255.0 },
      },
      vertexShader: brightnessVertexShader,
      fragmentShader: brightnessFragmentShader,
      side: THREE.DoubleSide,
      transparent: true,
    });
    return material;
  }, [options.brightness, options.alphaThreshold]); // Ensure initial uniform values are set if options change

  const sideMaterial = useMemo(() => {
    return new THREE.MeshBasicMaterial({ color: new THREE.Color(sideColor) });
  }, [sideColor]);

  const [currentImageData, setCurrentImageData] = useState<ImageData | null>(null);
  const [gifFramesData, setGifFramesData] = useState<ImageData[]>([]);
  const [gifTextures, setGifTextures] = useState<THREE.Texture[]>([]);
  const [staticTexture, setStaticTexture] = useState<THREE.Texture | null>(null);
  const [currentFrameIndex, setCurrentFrameIndex] = useState(0);
  const [isGif, setIsGif] = useState(false);

  // --- Image and GIF Processing ---
  useEffect(() => {
    const processImage = async () => {
      if (initialImageSrc) {
        setIsGif(false);
        setGifFramesData([]);
        setGifTextures([]);
        try {
          const img = await loadImageElement(initialImageSrc);
          const imageData = getImageDataFromImage(img, options.pixelationFactor);
          setCurrentImageData(imageData);
          if (onDimensionsChange && imageData) {
            onDimensionsChange({ width: imageData.width, height: imageData.height });
          }
          const tex = createTextureFromImageData(imageData);
          setStaticTexture(tex);
        } catch (error) {
          console.error('Error loading static image:', error);
          setCurrentImageData(null);
          setStaticTexture(null);
          if (onDimensionsChange) {
            onDimensionsChange({ width: 0, height: 0 });
          }
        }
      } else if (initialGifBuffer) {
        setIsGif(true);
        setStaticTexture(null);
        const frames: ImageData[] = [];
        const textures: THREE.Texture[] = [];
        try {
          const reader = new GifReader(new Uint8Array(initialGifBuffer));
          const frameCanvas = document.createElement('canvas');
          frameCanvas.width = reader.width;
          frameCanvas.height = reader.height;
          const frameCtx = frameCanvas.getContext('2d', { willReadFrequently: true });
          if (!frameCtx) {
            console.error('Could not get 2D context for GIF frame decoding.');
            throw new Error('Failed to get 2D context for GIF frame decoding.');
          }
          for (let i = 0; i < reader.numFrames(); i++) {
            const fullResImageData = frameCtx.createImageData(reader.width, reader.height);
            reader.decodeAndBlitFrameRGBA(i, fullResImageData.data);
            frameCtx.putImageData(fullResImageData, 0, 0);
            let processedImageData: ImageData;
            const pf = options.pixelationFactor || 1;
            if (pf > 1) {
              const frameTargetWidth = Math.max(1, Math.floor(reader.width / pf));
              const frameTargetHeight = Math.max(1, Math.floor(reader.height / pf));
              const pixelationTargetCanvas = document.createElement('canvas');
              pixelationTargetCanvas.width = frameTargetWidth;
              pixelationTargetCanvas.height = frameTargetHeight;
              const pixelationTargetCtx = pixelationTargetCanvas.getContext('2d', { willReadFrequently: true });
              if (!pixelationTargetCtx) {
                console.error('Could not get 2D context for GIF frame pixelation target, using full res.');
                processedImageData = frameCtx.getImageData(0, 0, reader.width, reader.height);
              } else {
                pixelationTargetCtx.drawImage(frameCanvas, 0, 0, frameTargetWidth, frameTargetHeight);
                processedImageData = pixelationTargetCtx.getImageData(0, 0, frameTargetWidth, frameTargetHeight);
              }
            } else {
              processedImageData = frameCtx.getImageData(0, 0, reader.width, reader.height);
            }
            frames.push(processedImageData);
            textures.push(createTextureFromImageData(processedImageData));
          }
          setGifFramesData(frames);
          setGifTextures(textures);
          if (frames.length > 0) {
            setCurrentImageData(frames[0]);
            if (onDimensionsChange && frames[0]) {
              onDimensionsChange({ width: frames[0].width, height: frames[0].height });
            }
          } else {
            setCurrentImageData(null);
            if (onDimensionsChange) {
              onDimensionsChange({ width: 0, height: 0 });
            }
          }
        } catch (error) {
          console.error('Error processing GIF:', error);
          setGifFramesData([]);
          setGifTextures([]);
          setCurrentImageData(null);
          if (onDimensionsChange) {
            onDimensionsChange({ width: 0, height: 0 });
          }
        }
      } else {
        // No image or gif buffer provided
        setCurrentImageData(null);
        setStaticTexture(null);
        setGifFramesData([]);
        setGifTextures([]);
        setIsGif(false);
        if (onDimensionsChange) {
          onDimensionsChange({ width: 0, height: 0 });
        }
      }
    }; // End of processImage async function

    processImage(); // Call the async function

  }, [initialImageSrc, initialGifBuffer, options.pixelationFactor, onDimensionsChange]); // End of useEffect for Image/GIF Processing

  // Effect to update geometry when currentImageData or options change
  useEffect(() => {
    if (!currentImageData) {
      if (geometryRef.current) {
        geometryRef.current.dispose();
        geometryRef.current = null;
      }
      if (meshRef.current) {
        meshRef.current.geometry = new THREE.BufferGeometry(); // Clear existing geometry
      }
      return; // Exit early if no image data
    }

    // currentImageData is guaranteed to be non-null here
    let newGeometry: THREE.BufferGeometry;
    if (options.legacy) {
      newGeometry = createLegacyGeometryInternal(currentImageData, options);
    } else {
      newGeometry = createGeometryFromImageDataInternal(currentImageData, options);
    }

    if (geometryRef.current) {
      geometryRef.current.dispose();
    }
    geometryRef.current = newGeometry;
    if (meshRef.current) {
      meshRef.current.geometry = newGeometry;
    }

    return () => {
      newGeometry?.dispose();
    };
  }, [currentImageData, options]); // End of useEffect for Geometry Update

  // --- Material and Texture Update ---
  useEffect(() => {
    let currentTextureToApply: THREE.Texture | null = null;
    if (isGif && gifTextures.length > 0) {
      currentTextureToApply = gifTextures[currentFrameIndex % gifTextures.length];
    } else if (staticTexture) {
      currentTextureToApply = staticTexture;
    }

    if (frontBackMaterial && frontBackMaterial.uniforms) { 
        frontBackMaterial.uniforms.uTexture.value = currentTextureToApply;
        frontBackMaterial.uniforms.uBrightness.value = options.brightness ?? 1.0;
        frontBackMaterial.uniforms.uAlphaTest.value = (options.alphaThreshold ?? 128) / 255.0;
        // frontBackMaterial.needsUpdate = true; // Not typically needed for uniform changes unless shader itself changes
    }
  }, [isGif, gifTextures, staticTexture, currentFrameIndex, frontBackMaterial, options.brightness, options.alphaThreshold]);

  // Animation (rotation and bobbing from original main.ts)
  useFrame((state, delta) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += 0.005;
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 1.5) * 0.02;
    }

    // GIF Animation Logic
    if (isGif && gifFramesData.length > 0 && gifTextures.length > 0 && options.animationSpeed && options.animationSpeed > 0) {
      const currentTime = state.clock.elapsedTime;
      const frameDuration = 1.0 / options.animationSpeed;

      if (currentTime - lastFrameChangeTimeRef.current >= frameDuration) {
        lastFrameChangeTimeRef.current = currentTime; // Update time of last frame change

        setCurrentFrameIndex(prevIndex => {
          const newRawIndex = prevIndex + 1;
          // Check if the frame that was just displayed (prevIndex) was the last one in the cycle
          if (gifTextures.length > 0 && (prevIndex % gifTextures.length === gifTextures.length - 1)) {
            console.log(`Displayed last GIF frame. Index in cycle: ${prevIndex % gifTextures.length}. Raw index: ${prevIndex}. About to loop/next cycle.`);
          }
          return newRawIndex;
        });
        // const frameImageData = gifFramesData[newFrameIndex];
        // setCurrentImageData(frameImageData); // This would trigger the geometry useEffect
      }
    }
  });

  // Don't render if no image data is ready (unless it's a GIF that might still be loading frames but has metadata)
  if (!currentImageData && !(isGif && (initialGifBuffer || gifFramesData.length > 0))) {
    return null;
  }

  return (
    <mesh 
      ref={meshRef} 
      material={[frontBackMaterial, sideMaterial]}
    >
      {/* Geometry is set via meshRef.current.geometry = newGeometry in another useEffect */}
    </mesh>
  );
};

export default ExtrudedImageMesh;
