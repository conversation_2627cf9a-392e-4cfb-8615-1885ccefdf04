"use client"
// import RemotePNGPlane from './RemotePNGPlane';
import { LayoutGroup, motion } from "motion/react"
import { TextRotate } from "@/components/ui/text-rotate"
import { FlowButton } from "@/components/flow-button"
import { PricingComponent } from "@/components/pricing-component"
import ResponsiveSquareGrid from "@/components/responsive-square-grid"
import ExtrudedImageViewer from './ThreeExtruder/ExtrudedImageViewer'; // Assuming relative path

interface MountainComponentProps {
  title?: string
}

export default function MountainComponent({ title = "Mountain Landscape" }: MountainComponentProps) {
  return (
    <>
      <div className="w-full">
        {/* Text Rotate Effect with improved container height and spacing */}
        <div className="w-full min-h-[20rem] pb-12 text-2xl sm:text-4xl md:text-6xl lg:text-7xl flex items-start justify-start font-sans bg-white dark:text-muted text-foreground font-light">
          <LayoutGroup>
            <motion.div className="flex flex-col items-start text-left" layout>
              {/* Line 1: Static Text */}
              <motion.span
                className="text-black font-bold"
                layout
                transition={{ type: "spring", damping: 25, stiffness: 450 }}
              >
                Greate Greaet Grea
              </motion.span>
              {/* Line 2: Rotating Text */}
              <div className="inline-block h-auto overflow-visible"> {/* Retaining original styling for TextRotate container */}
                <TextRotate
                  texts={[
                    "DASASDSDADAS",
                    "BDFDSFFDSs",
                    "SADDASDDASD",
                    "CADASDDSA",
                    "SADADASDASDAS",
                    "ADSASDASDAS",
                    "MASDASDns",
                  ]}
                  colors={[
                    "text-orange-600",
                    "text-red-600",
                    "text-green-600",
                    "text-gray-800",
                    "text-yellow-600",
                    "text-gray-600",
                    "text-pink-600",
                    "text-blue-600",
                  ]}
                  mainClassName="text-black font-bold overflow-visible justify-center rounded-lg"
                  staggerFrom={"last"}
                  initial={{ y: "100%" }}
                  animate={{ y: 0 }}
                  exit={{ y: "-130%" }}
                  staggerDuration={0.025}
                  splitLevelClassName="overflow-visible"
                  elementLevelClassName="overflow-visible"
                  transition={{
                    type: "spring",
                    damping: 20, // Back to bouncy spring animation!
                    stiffness: 500, // High stiffness for snappy response
                  }}
                  rotationInterval={3000}
                />
              </div>

              {/* Second line with proper spacing */}
              <motion.span
                className="text-black font-bold"
                layout
                transition={{ type: "spring", damping: 25, stiffness: 450 }}
              >
                only for w
              </motion.span>
            </motion.div>
          </LayoutGroup>
        </div>

        <div className="flex flex-col gap-6 items-start py-4">
          <FlowButton text="Get Started" size="xl" />
        </div>
      </div>
      <div className="w-full max-w-4xl mx-auto">
        {/* Dynamic Resizable Square Grid */}
        <ResponsiveSquareGrid className="mt-8" />

        {/* Pricing Section */}
        <div className="mt-16">
          <PricingComponent />
        </div>

        {/* Extruded Image Viewer Section (Borderless, No Controls) */}
        <div className="mt-16">
          <h3 className="text-xl font-semibold mb-4 text-center text-gray-800 dark:text-gray-200">3D Model Viewers</h3>
          <div className="flex flex-col md:flex-row gap-8 mt-4">
            {/* Blender Viewer */}
            <div style={{ flex: 1, minHeight: '70vh', position: 'relative', border: 'none', outline: 'none' }}>
              <ExtrudedImageViewer defaultPresetName="Blender" hideControlsPanel={true} defaultUseAsciiFilter={true} />
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2 text-center">Blender preset (ASCII, no controls, no border).</p>
            </div>
            {/* Lock Viewer */}
            <div style={{ flex: 1, minHeight: '70vh', position: 'relative', border: 'none', outline: 'none' }}>
              <ExtrudedImageViewer defaultPresetName="Lock" hideControlsPanel={true} defaultUseAsciiFilter={false} />
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2 text-center">Lock preset (No ASCII, no controls, no border).</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
