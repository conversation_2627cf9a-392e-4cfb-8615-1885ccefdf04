import React, {forwardRef, useImperativeHandle, useState} from 'react';
import './VideoHandler.scss';
import {ModeResolutionSelector} from './mode-resolution-selector/ModeResolutionSelector';
import { defaultPreset } from '../video-presets'; // Import defaultPreset for fallbacks
import {AutoResolutionSelector} from './resolution-parameters/AutoResolutionSelector';
import {ManualResolutionSelector} from './resolution-parameters/ManualResolutionSelector';

type Props = {
	videoRef: React.RefObject<HTMLVideoElement | null>;
	onCanPlay: () => void;
	setCharsPerLine: (charsPerLine: number) => void;
	setCharsPerColumn: (charsPerColumn: number) => void;
	autoPlay?: boolean;
	isPresetActive?: boolean;
	onManualFileSelected?: () => void;
	initialCharsPerLine?: number; // Added to receive from VideoAsciiPanel
	initialCharsPerColumn?: number; // Added to receive from VideoAsciiPanel
};

export type VideoHandlerRef = {
	ejectVideo: () => void;
};

export const VideoHandler = forwardRef((props: Props, ref) => {
	const inputRef = React.useRef<HTMLInputElement>(null);
	const [videoUrl, setVideoUrl] = useState<string | undefined>(undefined);
	const autoPlay = props.autoPlay ?? true;

	// Mode resolution selection
	const [useAutoAspectRatio, setUseAutoAspectRatio] = useState(true);

	// Settings for manual resolution
	const [manualCharsPerLine, setManualCharsPerLine] = useState(160);
	const [manualCharsPerColumn, setManualCharsPerColumn] = useState(90);

	// Settings to calculate the chars per line/column based on the image aspect ratio and a selected line/column base
	const [autoResolutionBase, setAutoResolutionBase] = useState(100);
	const [useLineBase, setUseLineBase] = useState(true);

	const calculateCharsPerLine = (video: HTMLVideoElement) => Math.round(autoResolutionBase * (video.videoWidth / video.videoHeight));
	const calculateCharsPerColumn = (video: HTMLVideoElement) => Math.round(autoResolutionBase * (video.videoHeight / video.videoWidth));

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (!file) {
			return;
		}
		props.onManualFileSelected?.(); // Notify parent about manual selection
		setVideoUrl(URL.createObjectURL(file));
	};

	useImperativeHandle(ref, () => ({
		ejectVideo() {
			setVideoUrl(undefined);
		},
	}));

	const onCanPlayHandler = () => {
		const video = props.videoRef.current;
		if (video) {
			if (props.isPresetActive) {
				const presetCharsPerLine = props.initialCharsPerLine ?? defaultPreset.charsPerLine; // Fallback if not provided
				props.setCharsPerLine(presetCharsPerLine);

				if (props.initialCharsPerColumn === 0 || props.initialCharsPerColumn === undefined) {
					// Auto-calculate charsPerColumn based on video aspect ratio and preset's charsPerLine
					if (video.videoWidth > 0 && video.videoHeight > 0) { // Ensure dimensions are available
						props.setCharsPerColumn(Math.round(presetCharsPerLine * (video.videoHeight / video.videoWidth)));
					} else {
						// Fallback if video dimensions aren't ready, though ideally they should be on canplay
						props.setCharsPerColumn(defaultPreset.charsPerColumn ?? 0); 
					}
				} else {
					// Use explicitly defined charsPerColumn from preset
					props.setCharsPerColumn(props.initialCharsPerColumn);
				}
			} else {
				// Manual mode: calculate and set based on user's resolution settings
				if (useAutoAspectRatio) {
					props.setCharsPerLine(useLineBase ? autoResolutionBase : calculateCharsPerLine(video));
					props.setCharsPerColumn(useLineBase ? calculateCharsPerColumn(video) : autoResolutionBase);
				} else {
					props.setCharsPerLine(manualCharsPerLine);
					props.setCharsPerColumn(manualCharsPerColumn);
				}
			}
		}
		props.onCanPlay(); // Notify VideoAsciiPanel
	};

  // Custom video loop logic using onended event
  const handleVideoEnded = React.useCallback(() => {
    const videoElement = props.videoRef.current;
    if (videoElement) {
      videoElement.loop = false; // Ensure loop property is false before restarting
      videoElement.load();
      videoElement.play().catch(error => {
        console.error("Error attempting to replay video after end:", error);
      });
    }
  }, [props.videoRef]);

  React.useEffect(() => {
    const videoElement = props.videoRef.current;
    if (videoElement) {
      // Ensure JavaScript 'loop' property is false, overriding external settings.
      // This allows the 'onended' event to fire for custom loop handling.
      videoElement.loop = false;
    }
  }, [props.videoRef, props.isPresetActive]);

	return (
		<>
			{/* Video element is always rendered for the ref, src is managed by VideoAsciiPanel for presets or videoUrl for manual */}
			<video 
				ref={props.videoRef} 
				src={videoUrl} // Populated for manual uploads, VideoAsciiPanel sets .src directly for presets
				style={{width: 0, height: 0, position: 'absolute', top: 0, left: 0}}
				onCanPlay={onCanPlayHandler}
				onEnded={handleVideoEnded} // Added for custom looping via onended event
				autoPlay={!props.isPresetActive && autoPlay} // autoPlay for manual mode, VideoAsciiPanel handles play for presets
				controls={false} // Always false
				crossOrigin="anonymous" // Added for CORS
				// loop is controlled by VideoAsciiPanel directly on videoRef.current for presets
			/>

			{/* Show input UI only if not in preset mode AND no manual video is loaded */}
			{!props.isPresetActive && !videoUrl && (
				<>
					<h1 className={'app-title'}>Video ASCII Player</h1>
					<div className={'mode-selector-container'}>
						<ModeResolutionSelector useAutoAspectRatio={useAutoAspectRatio}
							setUseAutoAspectRatio={setUseAutoAspectRatio}/>
					</div>

					<div className={'image-input-container'}>
						<div className={'image-settings'}>
							{
								useAutoAspectRatio
									? (
										<AutoResolutionSelector autoResolutionBase={autoResolutionBase}
											setAutoResolutionBase={setAutoResolutionBase}
											useLineBase={useLineBase}
											setUseLineBase={setUseLineBase}
										/>
									) : (
										<ManualResolutionSelector charsPerLine={manualCharsPerLine}
											charsPerColumn={manualCharsPerColumn}
											setCharsPerLine={setManualCharsPerLine}
											setCharsPerColumn={setManualCharsPerColumn}
										/>
									)
							}
						</div>
						<div className={'image-input-button'}>
							<input ref={inputRef} style={{display: 'none'}} type='file' accept='video/*'
								onChange={handleInputChange}/>
							<button className={'video-input-button'} onClick={() => {
								inputRef.current?.click();
							}}>
								Select video
							</button>
						</div>
					</div>
				</>
			)}
		</>
	);
});

VideoHandler.displayName = 'VideoHandler';
