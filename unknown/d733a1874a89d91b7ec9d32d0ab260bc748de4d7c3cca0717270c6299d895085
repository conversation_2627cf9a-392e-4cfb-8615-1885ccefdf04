"use client"

import { useState } from "react"
import { Heart, Share2, MessageCircle, ChevronDown, ChevronUp } from "lucide-react"

interface InteractiveCardProps {
  title?: string
  subtitle?: string
  initialLikes?: number
  theme?: "blue" | "purple" | "green" | "amber"
}

export default function InteractiveCard({
  title = "Interactive React Component",
  subtitle = "This component demonstrates React integration in the carousel",
  initialLikes = 42,
  theme = "blue",
}: InteractiveCardProps) {
  const [likes, setLikes] = useState(initialLikes)
  const [liked, setLiked] = useState(false)
  const [expanded, setExpanded] = useState(false)
  const [comments, setComments] = useState<string[]>([])
  const [newComment, setNewComment] = useState("")

  const themeColors = {
    blue: {
      bg: "from-blue-500 to-blue-700",
      button: "bg-blue-600 hover:bg-blue-700",
      light: "bg-blue-100 text-blue-800",
      border: "border-blue-300",
    },
    purple: {
      bg: "from-purple-500 to-purple-700",
      button: "bg-purple-600 hover:bg-purple-700",
      light: "bg-purple-100 text-purple-800",
      border: "border-purple-300",
    },
    green: {
      bg: "from-green-500 to-green-700",
      button: "bg-green-600 hover:bg-green-700",
      light: "bg-green-100 text-green-800",
      border: "border-green-300",
    },
    amber: {
      bg: "from-amber-500 to-amber-700",
      button: "bg-amber-600 hover:bg-amber-700",
      light: "bg-amber-100 text-amber-800",
      border: "border-amber-300",
    },
  }

  const handleLike = () => {
    if (liked) {
      setLikes(likes - 1)
    } else {
      setLikes(likes + 1)
    }
    setLiked(!liked)
  }

  const handleAddComment = () => {
    if (newComment.trim()) {
      setComments([...comments, newComment])
      setNewComment("")
    }
  }

  return (
    <div className="w-full max-w-3xl mx-auto">
      <div className={`rounded-lg overflow-hidden shadow-lg bg-gradient-to-r ${themeColors[theme].bg}`}>
        <div className="p-6 text-white">
          <h2 className="text-2xl font-bold mb-2">{title}</h2>
          <p className="text-white/80">{subtitle}</p>

          <div className="mt-6 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleLike}
                className={`flex items-center space-x-1 transition-all ${
                  liked ? "text-red-300 scale-110" : "text-white/80 hover:text-white"
                }`}
              >
                <Heart className={`h-5 w-5 ${liked ? "fill-red-300" : ""}`} />
                <span>{likes}</span>
              </button>

              <button
                onClick={() => setExpanded(!expanded)}
                className="flex items-center space-x-1 text-white/80 hover:text-white transition-all"
              >
                <MessageCircle className="h-5 w-5" />
                <span>{comments.length}</span>
              </button>

              <button className="flex items-center space-x-1 text-white/80 hover:text-white transition-all">
                <Share2 className="h-5 w-5" />
              </button>
            </div>

            <button onClick={() => setExpanded(!expanded)} className="text-white/80 hover:text-white transition-all">
              {expanded ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
            </button>
          </div>
        </div>

        {expanded && (
          <div className="bg-white p-6 transition-all">
            <h3 className="font-semibold mb-4">Comments ({comments.length})</h3>

            <div className="mb-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Add a comment..."
                  className="flex-1 px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") handleAddComment()
                  }}
                />
                <button
                  onClick={handleAddComment}
                  className={`px-4 py-2 text-white rounded-lg ${themeColors[theme].button}`}
                >
                  Post
                </button>
              </div>
            </div>

            <div className="space-y-3 max-h-60 overflow-y-auto">
              {comments.length > 0 ? (
                comments.map((comment, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg ${themeColors[theme].light} ${themeColors[theme].border} border`}
                  >
                    {comment}
                  </div>
                ))
              ) : (
                <div className="text-gray-500 text-center py-4">No comments yet. Be the first to comment!</div>
              )}
            </div>

            <div className="mt-6 space-y-4">
              <div className={`p-4 rounded-lg ${themeColors[theme].light} border ${themeColors[theme].border}`}>
                <h4 className="font-medium mb-2">React Component Features</h4>
                <ul className="list-disc pl-5 space-y-1">
                  <li>State management with useState</li>
                  <li>Event handling with onClick</li>
                  <li>Conditional rendering</li>
                  <li>Form inputs and controlled components</li>
                  <li>Dynamic styling based on props</li>
                </ul>
              </div>

              <div className="text-sm text-gray-500 text-center">
                This component demonstrates how React components can be integrated into the carousel with full
                interactivity.
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
