"use client"

import { forwardRef, use<PERSON><PERSON>back, useEffect, useImperativeHandle, useMemo, useState } from "react"
import { AnimatePresence, type AnimatePresenceProps, motion, type MotionProps, type Transition } from "motion/react"

import { cn } from "@/lib/utils"

interface TextRotateProps {
  texts: string[]
  colors?: string[] // Array of color classes to apply to each text
  rotationInterval?: number
  initial?: MotionProps["initial"]
  animate?: MotionProps["animate"]
  exit?: MotionProps["exit"]
  animatePresenceMode?: AnimatePresenceProps["mode"]
  animatePresenceInitial?: boolean
  staggerDuration?: number
  staggerFrom?: "first" | "last" | "center" | number | "random"
  transition?: Transition
  loop?: boolean // Whether to start from the first text when the last one is reached
  auto?: boolean // Whether to start the animation automatically
  splitBy?: "words" | "characters" | "lines" | string
  onNext?: (index: number) => void
  mainClassName?: string
  splitLevelClassName?: string
  elementLevelClassName?: string
}

export interface TextRotateRef {
  next: () => void
  previous: () => void
  jumpTo: (index: number) => void
  reset: () => void
}

interface WordObject {
  characters: string[]
  needsSpace: boolean
}

const TextRotate = forwardRef<TextRotateRef, TextRotateProps>(
  (
    {
      texts,
      colors = [],
      transition = { type: "spring", damping: 20, stiffness: 500 },
      initial = { y: "100%", opacity: 0 },
      animate = { y: 0, opacity: 1 },
      exit = { y: "-120%", opacity: 0 },
      animatePresenceMode = "wait",
      animatePresenceInitial = false,
      rotationInterval = 2000,
      staggerDuration = 0,
      staggerFrom = "first",
      loop = true,
      auto = true,
      splitBy = "characters",
      onNext,
      mainClassName,
      splitLevelClassName,
      elementLevelClassName,
      ...props
    },
    ref,
  ) => {
    const [currentTextIndex, setCurrentTextIndex] = useState(0)
    const [isAnimationComplete, setIsAnimationComplete] = useState(false)

    // handy function to split text into characters with support for unicode and emojis
    const splitIntoCharacters = (text: string): string[] => {
      if (typeof Intl !== "undefined" && "Segmenter" in Intl) {
        const segmenter = new Intl.Segmenter("en", { granularity: "grapheme" })
        return Array.from(segmenter.segment(text), ({ segment }) => segment)
      }
      // Fallback for browsers that don't support Intl.Segmenter
      return Array.from(text)
    }

    const elements = useMemo(() => {
      const currentText = texts[currentTextIndex]
      if (splitBy === "characters") {
        const text = currentText.split(" ")
        return text.map((word, i) => ({
          characters: splitIntoCharacters(word),
          needsSpace: i !== text.length - 1,
        }))
      }
      return splitBy === "words"
        ? currentText.split(" ")
        : splitBy === "lines"
          ? currentText.split("\n")
          : currentText.split(splitBy)
    }, [texts, currentTextIndex, splitBy])

    const getStaggerDelay = useCallback(
      (index: number, totalChars: number) => {
        const total = totalChars
        if (staggerFrom === "first") return index * staggerDuration
        if (staggerFrom === "last") return (total - 1 - index) * staggerDuration
        if (staggerFrom === "center") {
          const center = Math.floor(total / 2)
          return Math.abs(center - index) * staggerDuration
        }
        if (staggerFrom === "random") {
          const randomIndex = Math.floor(Math.random() * total)
          return Math.abs(randomIndex - index) * staggerDuration
        }
        return Math.abs(staggerFrom - index) * staggerDuration
      },
      [staggerFrom, staggerDuration],
    )

    // Helper function to handle index changes and trigger callback
    const handleIndexChange = useCallback(
      (newIndex: number) => {
        setIsAnimationComplete(false) // Reset animation state
        setCurrentTextIndex(newIndex)
        onNext?.(newIndex)
      },
      [onNext],
    )

    const next = useCallback(() => {
      const nextIndex = currentTextIndex === texts.length - 1 ? (loop ? 0 : currentTextIndex) : currentTextIndex + 1

      if (nextIndex !== currentTextIndex) {
        handleIndexChange(nextIndex)
      }
    }, [currentTextIndex, texts.length, loop, handleIndexChange])

    const previous = useCallback(() => {
      const prevIndex = currentTextIndex === 0 ? (loop ? texts.length - 1 : currentTextIndex) : currentTextIndex - 1

      if (prevIndex !== currentTextIndex) {
        handleIndexChange(prevIndex)
      }
    }, [currentTextIndex, texts.length, loop, handleIndexChange])

    const jumpTo = useCallback(
      (index: number) => {
        const validIndex = Math.max(0, Math.min(index, texts.length - 1))
        if (validIndex !== currentTextIndex) {
          handleIndexChange(validIndex)
        }
      },
      [texts.length, currentTextIndex, handleIndexChange],
    )

    const reset = useCallback(() => {
      if (currentTextIndex !== 0) {
        handleIndexChange(0)
      }
    }, [currentTextIndex, handleIndexChange])

    // Expose all navigation functions via ref
    useImperativeHandle(
      ref,
      () => ({
        next,
        previous,
        jumpTo,
        reset,
      }),
      [next, previous, jumpTo, reset],
    )

    useEffect(() => {
      if (!auto) return
      const intervalId = setInterval(next, rotationInterval)
      return () => clearInterval(intervalId)
    }, [next, rotationInterval, auto])

    // Get the current color for this specific text index
    const getCurrentColor = (textIndex: number) => {
      return colors.length > 0 ? colors[textIndex % colors.length] : ""
    }

    // Calculate total characters for animation completion tracking
    const totalCharacters = useMemo(() => {
      if (splitBy === "characters") {
        return (elements as WordObject[]).reduce((sum, word) => sum + word.characters.length, 0)
      }
      return (elements as string[]).length
    }, [elements, splitBy])

    // Track animation completion
    const handleAnimationComplete = useCallback(() => {
      setIsAnimationComplete(true)
    }, [])

    return (
      <motion.span
        className={cn("flex flex-wrap whitespace-pre-wrap", mainClassName)}
        {...props}
        layout
        transition={transition}
      >
        <span className="sr-only">{texts[currentTextIndex]}</span>

        <div
          className="relative overflow-hidden"
          style={{
            paddingTop: "0.08em",
            paddingBottom: "0.1em",
            willChange: "transform",
            transform: "translateZ(0)",
          }}
        >
          <AnimatePresence mode={animatePresenceMode} initial={animatePresenceInitial}>
            <motion.div
              key={currentTextIndex}
              className={cn(
                "flex flex-wrap",
                splitBy === "lines" && "flex-col w-full",
                getCurrentColor(currentTextIndex),
              )}
              layout="position"
              layoutId="rotating-text"
              style={{
                willChange: "transform",
                // Lock position when animation is complete to prevent trembling
                transform: isAnimationComplete ? "translate3d(0, 0, 0)" : undefined,
              }}
              transition={{
                layout: {
                  type: "spring",
                  damping: 25,
                  stiffness: 400,
                },
              }}
              aria-hidden="true"
            >
              {(splitBy === "characters"
                ? (elements as WordObject[])
                : (elements as string[]).map((el, i) => ({
                    characters: [el],
                    needsSpace: i !== elements.length - 1,
                  }))
              ).map((wordObj, wordIndex, array) => {
                const previousCharsCount = array
                  .slice(0, wordIndex)
                  .reduce((sum, word) => sum + word.characters.length, 0)

                return (
                  <span key={wordIndex} className={cn("inline-flex", splitLevelClassName)}>
                    {wordObj.characters.map((char, charIndex) => {
                      const globalCharIndex = previousCharsCount + charIndex
                      const isLastChar = globalCharIndex === totalCharacters - 1

                      return (
                        <motion.span
                          initial={initial}
                          animate={animate}
                          exit={exit}
                          key={charIndex}
                          style={{
                            willChange: "transform",
                            // Lock position when animation is complete to prevent trembling
                            transform: isAnimationComplete ? "translate3d(0, 0, 0)" : undefined,
                          }}
                          transition={{
                            ...transition,
                            delay: getStaggerDelay(globalCharIndex, totalCharacters),
                            // Keep the fun bouncy spring animation
                            type: "spring",
                            damping: 20, // Lower damping for more bounce
                            stiffness: 500, // High stiffness for snappy response
                          }}
                          onAnimationComplete={isLastChar ? handleAnimationComplete : undefined}
                          className={cn("inline-block", elementLevelClassName)}
                        >
                          {char}
                        </motion.span>
                      )
                    })}
                    {wordObj.needsSpace && <span className="whitespace-pre"> </span>}
                  </span>
                )
              })}
            </motion.div>
          </AnimatePresence>
        </div>
      </motion.span>
    )
  },
)

TextRotate.displayName = "TextRotate"

export { TextRotate }
