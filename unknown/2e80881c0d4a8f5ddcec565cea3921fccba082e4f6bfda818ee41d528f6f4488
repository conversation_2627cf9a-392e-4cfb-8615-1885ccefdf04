"use client"

import { Waves } from "lucide-react"

interface OceanComponentProps {
  title?: string
}

export default function OceanComponent({ title = "Ocean Sunset" }: OceanComponentProps) {
  return (
    <div className="w-full max-w-3xl mx-auto p-6 bg-gradient-to-b from-orange-100 to-amber-50 rounded-lg shadow-md">
      <div className="flex items-center justify-center mb-6">
        <Waves className="h-16 w-16 text-amber-600" />
      </div>
      <h2 className="text-3xl font-bold text-center text-amber-800 mb-4">{title}</h2>
      <p className="text-xl text-amber-700 text-center">
        Golden light dancing across gentle waves as the sun meets the horizon in a spectacular display.
      </p>
    </div>
  )
}
