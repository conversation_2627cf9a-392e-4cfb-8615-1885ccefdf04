import React from 'react'
import { EmblaOptionsType } from 'embla-carousel'
import { DotButton, useDotButton } from './EmblaCarouselDotButton'

import useEmblaCarousel from 'embla-carousel-react'
import Autoplay from 'embla-carousel-autoplay'

import { SlidePreset } from './slidePresets';

interface Props {
  slides: SlidePreset[]
  options?: EmblaOptionsType
  autoplayDelay?: number
  className?: string
};

export const EmblaCarousel: React.FC<Props> = (props) => {
  const { slides, options, autoplayDelay = 2000, className } = props
  const [emblaRef, emblaApi] = useEmblaCarousel(options, [
    Autoplay({
      delay: autoplayDelay,
      stopOnInteraction: false,
    }),
  ])

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi)

  return (
    <section className={`embla ${className || ''}`.trim()}>
      <div className="embla__viewport" ref={emblaRef}>
        <div className="embla__container">
          {slides.map((slide) => (
            <div className="embla__slide" key={slide.id}>
              <div className="embla__slide__number">
                <div style={{
                  fontFamily: 'monospace',
                  fontSize: '1rem',
                  padding: '1em',
                  margin: '0.5em',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word',
                  minHeight: '100px',
                  boxSizing: 'border-box',
                  width: 'calc(100% - 1em)',
                  borderWidth: '12px', 
                  borderStyle: 'solid',
                  borderColor: 'transparent',
                  borderImageSource: `url("data:image/svg+xml,%3Csvg width='30' height='30' viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cstyle%3Etext { font-family: monospace; font-size: 10px; fill: %23000; }%3C/style%3E%3C/defs%3E%3Ctext x='2' y='8'%3E+%3C/text%3E%3Ctext x='22' y='8'%3E+%3C/text%3E%3Ctext x='2' y='28'%3E+%3C/text%3E%3Ctext x='22' y='28'%3E+%3C/text%3E%3Ctext x='12' y='8'%3E-%3C/text%3E%3Ctext x='12' y='28'%3E-%3C/text%3E%3Ctext x='2' y='18'%3E|%3C/text%3E%3Ctext x='22' y='18'%3E|%3C/text%3E%3C/svg%3E")`,
                  borderImageSlice: 10,
                  borderImageRepeat: 'repeat'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5em' }}>
                    <img src={slide.iconContent} alt={slide.title + " icon"} style={{ marginRight: '8px' }} width="30px" height="30px" />
                    <div style={{ fontWeight: 'bold' }}>{slide.title}</div>
                  </div>
                  <div>{slide.slideContent}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>


    </section>
  )
}

export default EmblaCarousel
