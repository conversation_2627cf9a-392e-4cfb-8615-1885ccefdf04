export interface VideoPreset {
  name: string;
  remoteUrl: string;
  charsPerLine: number;
  charsPerColumn?: number; // Made optional for auto-calculation
  loop: boolean;
  characterSize?: number; // Added for custom font size per preset
}

export const videoPresets: VideoPreset[] = [
  {
    name: 'Planet',
    remoteUrl: 'https://raw.githubusercontent.com/delaysystem/MiniReader/refs/heads/master/file_example_MP4_480_1_5MG.mp4',
    charsPerLine: 90,
    loop: true,
    characterSize: 2,
  },
  {
    name: 'Shit',
    remoteUrl: 'https://media.githubusercontent.com/media/yuhuanq/prdoc/f1e4f347437f6d114cfa2651a455d6ba913dc22e/real.mp4',
    charsPerLine: 85,
    loop: false,
    characterSize: 10,
  },
  {
    name: 'FASDADSFDSF',
    remoteUrl: 'https://media.githubusercontent.com/media/yuhuanq/prdoc/f1e4f347437f6d114cfa2651a455d6ba913dc22e/girl_source.mp4', 
    charsPerLine: 80,
    loop: true,
    characterSize: 6,
  },
  {
    name: 'Gears',
    remoteUrl: 'https://raw.githubusercontent.com/SurrealSystems/Surreal/refs/heads/main/vecteezy_configuring-screen-with-gears-rotating-ideal-for-under_2498667%20(online-video-cutter-negate.mp4', 
    charsPerLine: 60,
    loop: true,
    characterSize: 6,
  },
  {
    name: 'Bell',
    remoteUrl: 'https://raw.githubusercontent.com/SurrealSystems/Surreal/refs/heads/main/193-bell-notification-ezgif-negate.mp4', 
    charsPerLine: 60,
    loop: true,
    characterSize: 6,
  },
];

export const defaultPreset: VideoPreset = videoPresets[0];
