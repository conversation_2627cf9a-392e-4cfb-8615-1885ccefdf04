@mixin base-color {
  opacity: 0.7;
  border-color: transparent transparent transparent #d3d3d3;
}

@mixin base-color-hover {
  opacity: 1;
}

@mixin base-color-background {
  background: #d3d3d3;
  opacity: 0.7;
}

@mixin base-color-background-hover {
  opacity: 1;
}

/* Panel */
.video-controller-panel {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;

  .button-play-pause {
    width: 5em;
    margin-left: 1em;
  }

  .speaker {
    width: 4em;
    margin-left: 1em;
  }

  .slider-video-position {
    width: 40%;
  }

  .slider-video-volume {
    width: 20%;
  }

  .button-eject-video {
    margin-left: 2em;
    margin-right: 1em;
  }
}

/* Play Pause button */
.button-play-pause {
  width: 3em;
  height: 3em;

  background: transparent;
  box-sizing: border-box;

  @include base-color;
  transition: 100ms all ease;
  cursor: pointer;

  border-style: solid;
  border-width: 1.5em 0 1.5em 2em;

  &.paused {
    border-style: double;
    border-width: 0 0 0 2em;
  }

  &:hover {
    @include base-color-hover;
  }
}

/* Speaker */
.speaker {
  width: 4em;
  height: 3em;

  position: relative;
  display: inline-block;

  @include base-color;

  span {
    // Speaker square
    display: block;
    width: 1em;
    height: 0.8em;
    background: #fff;
    margin: 1.05em 0 0 0.2em;

    // Speaker triangle
    &:after {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
      border-style: solid;
      border-color: transparent #fff transparent transparent;
      border-width: 0.8em 1.2em 0.8em 1.5em;
      left: -1.3em;
      top: 0.65em;
    }

    // Waves
    &:before {
      transform: rotate(45deg);
      border-radius: 0 1em 0 0;
      content: '';
      position: absolute;
      width: 0.7em;
      height: 0.7em;
      border-style: double;
      border-color: #fff;
      border-width: 0.4em 0.4em 0 0;
      transition: all 0.2s ease-out;
      top: 30%;
      left: 40%;
    }
  }

  &:hover {
    @include base-color-hover;

    span:before {
      transform: scale(.8) translate(-0.2em, 0) rotate(42deg);
    }
  }

  &.mute {
    span:before {
      transform: scale(.5) translate(-1em, 0) rotate(36deg);
      opacity: 0;
    }
  }
}

/* Slider video position */
.slider-video-position {
  -webkit-appearance: none;
  width: 100%;
  height: 1em;
  border-radius: 2em;
  outline: none;
  @include base-color;
  -webkit-transition: .2s;
  transition: opacity .2s;

  &:hover {
    @include base-color-hover;
  }

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 2.25em;
    height: 2.25em;
    border-radius: 50%;
    background: dimgray;
    cursor: pointer;
  }
}

/* Slider video volume */
.slider-video-volume {
  -webkit-appearance: none;
  width: 100%;
  height: 0.8em;
  outline: none;
  @include base-color;
  -webkit-transition: .2s;
  transition: opacity .2s;
  border-radius: 30%;

  &:hover {
    @include base-color-hover;
  }

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    border-radius: 30%;
    appearance: none;
    width: 2.25em;
    height: 2.25em;
    background: dimgray;
    cursor: pointer;
  }
}

.button-eject-video {
  position: relative;
  width: 2.5em;
  height: 2.5em;

  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1.8em;
    height: 0.3em;
    background-color: black;
    transform: translate(-50%, -50%) rotate(45deg);
  }

  &::after {
    transform: translate(-50%, -50%) rotate(-45deg);
  }
}
