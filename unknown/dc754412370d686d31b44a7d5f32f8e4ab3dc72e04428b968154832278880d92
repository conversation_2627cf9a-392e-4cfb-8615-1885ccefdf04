// app/not-found.tsx
'use client';

import { useState, useEffect } from 'react';

export default function NotFound() {
  const [errorMessage, setErrorMessage] = useState<string>('Loading error message...');

  useEffect(() => {
    async function fetchErrorMessage() {
      try {
        const response = await fetch('/error-messages/404.txt');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const text = await response.text();
        setErrorMessage(text);
      } catch (error) {
        console.error('Failed to load 404 error message:', error);
        setErrorMessage('Failed to load the custom 404 message. Please try again later.');
      }
    }

    fetchErrorMessage();
  }, []);

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      fontWeight: 'bold',
      textAlign: 'left',
      fontFamily: 'monospace',
      whiteSpace: 'pre-wrap',
      padding: '20px',
    }}>

        {errorMessage}
      
    </div>
  );
}
