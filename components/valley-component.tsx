"use client"

import { Sprout } from "lucide-react"

interface ValleyComponentProps {
  title?: string
}

export default function ValleyComponent({ title = "Green Valley" }: ValleyComponentProps) {
  return (
    <div className="w-full max-w-3xl mx-auto p-6 bg-gradient-to-b from-lime-100 to-green-50 rounded-lg shadow-md">
      <div className="flex items-center justify-center mb-6">
        <Sprout className="h-16 w-16 text-lime-600" />
      </div>
      <h2 className="text-3xl font-bold text-center text-lime-800 mb-4">{title}</h2>
      <p className="text-xl text-lime-700 text-center">
        Rolling hills of vibrant green stretching as far as the eye can see, nestled between protective mountains.
      </p>
    </div>
  )
}
