"use client"

import { <PERSON>, ArrowR<PERSON>, ArrowLeft, Menu, Home, User, Setting<PERSON>, Search, Bell, Heart, Star } from "lucide-react"
import { useState } from "react"

export default function Footer() {
  const [isRightExpanded, setIsRightExpanded] = useState(false)
  const [isLeftExpanded, setIsLeftExpanded] = useState(false)

  const handleRightMouseEnter = () => {
    setIsRightExpanded(true)
    setIsLeftExpanded(false) // Hide left button when right is expanded
  }

  const handleLeftMouseEnter = () => {
    setIsLeftExpanded(true)
    setIsRightExpanded(false) // Hide right button when left is expanded
  }

  const handleMouseLeave = () => {
    setIsRightExpanded(false)
    setIsLeftExpanded(false)
  }

  return (
    <footer
      className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-1/3 bg-gray-200/80 backdrop-blur-md z-50 rounded-t-lg overflow-hidden"
      onMouseLeave={handleMouseLeave}
    >
      <div className="relative h-16">
        {/* Original footer content - logo centered */}
        <div
          className={`flex items-center justify-center h-full transition-all duration-500 ease-in-out ${
            isRightExpanded || isLeftExpanded ? "opacity-0" : "opacity-100"
          }`}
        >
          <Mountain className="h-8 w-8 text-gray-700" />
        </div>

        {/* Left Interactive button container */}
        <div
          className={`absolute left-0 top-0 h-full z-10 transition-all duration-500 ease-in-out ${
            isRightExpanded ? "opacity-0 pointer-events-none" : "opacity-100"
          }`}
          onMouseEnter={handleLeftMouseEnter}
        >
          {/* Left arrow button */}
          <button
            className={`h-full px-6 bg-gray-200/60 backdrop-blur-md border-r border-gray-300/50 rounded-tl-lg hover:bg-gray-300/60 transition-all duration-500 ease-in-out flex items-center justify-center ${
              isLeftExpanded ? "transform translate-x-full opacity-0" : "transform translate-x-0 opacity-100"
            }`}
          >
            <ArrowLeft className="h-5 w-5 text-gray-700" />
          </button>
        </div>

        {/* Right Interactive button container */}
        <div
          className={`absolute right-0 top-0 h-full z-10 transition-all duration-500 ease-in-out ${
            isLeftExpanded ? "opacity-0 pointer-events-none" : "opacity-100"
          }`}
          onMouseEnter={handleRightMouseEnter}
        >
          {/* Right arrow button */}
          <button
            className={`h-full px-6 bg-gray-200/60 backdrop-blur-md border-l border-gray-300/50 rounded-tr-lg hover:bg-gray-300/60 transition-all duration-500 ease-in-out flex items-center justify-center ${
              isRightExpanded ? "transform -translate-x-full opacity-0" : "transform translate-x-0 opacity-100"
            }`}
          >
            <ArrowRight className="h-5 w-5 text-gray-700" />
          </button>
        </div>

        {/* Left Expanded content layer */}
        <div
          className={`absolute top-0 left-0 h-full w-full bg-gray-200/60 backdrop-blur-md rounded-t-lg transition-all duration-500 ease-in-out flex items-center justify-between px-6 ${
            isLeftExpanded ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-full"
          }`}
        >
          <div className="flex items-center space-x-4">
            <Search className="h-5 w-5 text-gray-700" />
            <span className="text-gray-700 font-medium">Discover</span>
          </div>

          <div className="flex items-center space-x-2">
            <button className="p-2 hover:bg-gray-300/40 rounded-lg transition-colors group">
              <Bell className="h-4 w-4 text-gray-700 group-hover:text-gray-900" />
            </button>
            <button className="p-2 hover:bg-gray-300/40 rounded-lg transition-colors group">
              <Heart className="h-4 w-4 text-gray-700 group-hover:text-gray-900" />
            </button>
            <button className="p-2 hover:bg-gray-300/40 rounded-lg transition-colors group">
              <Star className="h-4 w-4 text-gray-700 group-hover:text-gray-900" />
            </button>
          </div>
        </div>

        {/* Right Expanded content layer */}
        <div
          className={`absolute top-0 left-0 h-full w-full bg-gray-200/60 backdrop-blur-md rounded-t-lg transition-all duration-500 ease-in-out flex items-center justify-between px-6 ${
            isRightExpanded ? "opacity-100 translate-x-0" : "opacity-0 translate-x-full"
          }`}
        >
          <div className="flex items-center space-x-4">
            <Menu className="h-5 w-5 text-gray-700" />
            <span className="text-gray-700 font-medium">Quick Actions</span>
          </div>

          <div className="flex items-center space-x-2">
            <button className="p-2 hover:bg-gray-300/40 rounded-lg transition-colors group">
              <Home className="h-4 w-4 text-gray-700 group-hover:text-gray-900" />
            </button>
            <button className="p-2 hover:bg-gray-300/40 rounded-lg transition-colors group">
              <User className="h-4 w-4 text-gray-700 group-hover:text-gray-900" />
            </button>
            <button className="p-2 hover:bg-gray-300/40 rounded-lg transition-colors group">
              <Settings className="h-4 w-4 text-gray-700 group-hover:text-gray-900" />
            </button>
          </div>
        </div>
      </div>
    </footer>
  )
}
