"use client"

import type React from "react"

import { useEffect, useRef, useState, useCallback, useMemo } from "react"
import Script from "next/script"
import { createRoot, type Root } from "react-dom/client"

interface GlideCarouselProps {
  slides: {
    id: number
    image: string
    title: string
    contentFile?: string // Path to HTML content file
    contentComponent?: React.ComponentType<any> // React component
    contentProps?: any // Props to pass to the React component
    contentType?: "html" | "react" // Explicit content type
  }[]
  margin?: {
    top?: string
    bottom?: string
    left?: string
    right?: string
  }
  contentMargin?: {
    top?: string
    bottom?: string
    left?: string
    right?: string
  }
}

interface LoadedContent {
  html: string
  css?: string
  js?: string
}

export default function GlideCarousel({ slides, margin = {}, contentMargin = {} }: GlideCarouselProps) {
  // Set default margin values
  const { top = "2rem", bottom = "4rem", left = "0rem", right = "0rem" } = margin
  const {
    top: contentTop = "10rem",
    bottom: contentBottom = "0rem",
    left: contentLeft = "0rem",
    right: contentRight = "0rem",
  } = contentMargin

  const glideRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const glideInitialized = useRef(false)
  const glideInstance = useRef<any>(null)
  const [activeIndex, setActiveIndex] = useState(0)
  const [selectedCard, setSelectedCard] = useState<number | null>(null)
  const [loadedContent, setLoadedContent] = useState<Record<number, LoadedContent>>({})
  const [isContentVisible, setIsContentVisible] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const loadedScripts = useRef<Set<string>>(new Set())
  const reactRoots = useRef<Map<number, Root>>(new Map())
  const transitionTimeoutRef = useRef<NodeJS.Timeout>()
  const contentUpdateTimeoutRef = useRef<NodeJS.Timeout>()
  const lastActiveIndexRef = useRef(0)

  const glideOptions = {
    type: "carousel",
    startAt: 0,
    perView: 1,
    gap: 6,
    autoplay: false,
    hoverpause: true,
    animationDuration: 400, // Reduced for smoother transitions
    rewind: false,
    peek: { before: 100, after: 100 },
    bound: false,
    focusAt: "center",
    breakpoints: {},
  }

  // Memoize slide content to prevent unnecessary re-renders
  const memoizedSlides = useMemo(() => slides, [slides])

  // Debounced content update to prevent rapid state changes during transitions
  const debouncedContentUpdate = useCallback(
    (slideIndex: number) => {
      if (contentUpdateTimeoutRef.current) {
        clearTimeout(contentUpdateTimeoutRef.current)
      }

      contentUpdateTimeoutRef.current = setTimeout(() => {
        const slide = memoizedSlides[slideIndex]
        if (slide?.contentFile || slide?.contentComponent) {
          loadSlideContent(slideIndex)
        } else {
          hideContent()
        }
      }, 100) // Small delay to ensure smooth transitions
    },
    [memoizedSlides],
  )

  // Optimized content display with better error handling and performance
  const displayContent = useCallback(
    (slideIndex: number, content?: LoadedContent) => {
      if (!contentRef.current || isTransitioning) return

      const slide = memoizedSlides[slideIndex]
      if (!slide) return

      // Determine content type
      const contentType = slide.contentType || (slide.contentComponent ? "react" : "html")

      // Use requestAnimationFrame for smooth DOM updates
      requestAnimationFrame(() => {
        if (!contentRef.current) return

        // Clear previous content with fade effect
        contentRef.current.style.opacity = "0"

        setTimeout(() => {
          if (!contentRef.current) return

          // Clear previous content
          contentRef.current.innerHTML = ""

          // Remove previous styles
          const existingStyles = document.querySelectorAll("[data-carousel-style]")
          existingStyles.forEach((style) => style.remove())

          // Cleanup previous React root with proper error handling
          const existingRoot = reactRoots.current.get(slideIndex)
          if (existingRoot) {
            setTimeout(() => {
              try {
                existingRoot.unmount()
                reactRoots.current.delete(slideIndex)
              } catch (error) {
                console.warn(`Error unmounting root for slide ${slideIndex}:`, error)
              }
            }, 0)
          }

          if (contentType === "react" && slide.contentComponent) {
            // Handle React component rendering with error boundary
            const ComponentToRender = slide.contentComponent
            const componentProps = slide.contentProps || {}

            // Create wrapper div for React component with proper margins
            const componentWrapper = document.createElement("div")
            componentWrapper.className = "card-content-wrapper react-component-wrapper"
            componentWrapper.style.padding = `${contentTop} ${contentRight} ${contentBottom} ${contentLeft}`
            componentWrapper.style.opacity = "0"
            componentWrapper.style.transition = "opacity 0.3s ease"

            contentRef.current.appendChild(componentWrapper)

            // Create and store React root with error handling
            setTimeout(() => {
              try {
                const root = createRoot(componentWrapper)
                reactRoots.current.set(slideIndex, root)

                // Render the React component with error boundary
                root.render(
                  <div className="component-error-boundary">
                    <ComponentToRender {...componentProps} />
                  </div>,
                )

                // Fade in the component
                setTimeout(() => {
                  componentWrapper.style.opacity = "1"
                }, 50)
              } catch (error) {
                console.error(`Error rendering React component for slide ${slideIndex}:`, error)
              }
            }, 50)
          } else if (contentType === "html") {
            // Handle HTML content with improved performance
            const contentToDisplay = content || loadedContent[slideIndex]
            if (!contentToDisplay) return

            // Add CSS with margin variables injected
            if (contentToDisplay.css) {
              const styleElement = document.createElement("style")
              styleElement.setAttribute("data-carousel-style", "true")

              // Inject margin variables into the CSS
              let processedCSS = contentToDisplay.css
              processedCSS = processedCSS.replace(/var$$--carousel-margin-top$$/g, top)
              processedCSS = processedCSS.replace(/var$$--carousel-margin-bottom$$/g, bottom)
              processedCSS = processedCSS.replace(/var$$--carousel-margin-left$$/g, left)
              processedCSS = processedCSS.replace(/var$$--carousel-margin-right$$/g, right)
              processedCSS = processedCSS.replace(/var$$--content-margin-top$$/g, contentTop)
              processedCSS = processedCSS.replace(/var$$--content-margin-bottom$$/g, contentBottom)
              processedCSS = processedCSS.replace(/var$$--content-margin-left$$/g, contentLeft)
              processedCSS = processedCSS.replace(/var$$--content-margin-right$$/g, contentRight)

              styleElement.textContent = processedCSS
              document.head.appendChild(styleElement)
            }

            // Process HTML content with fade-in effect
            const contentWrapper = document.createElement("div")
            contentWrapper.className = "card-content-wrapper html-content-wrapper"
            contentWrapper.style.padding = `${contentTop} ${contentRight} ${contentBottom} ${contentLeft}`
            contentWrapper.style.opacity = "0"
            contentWrapper.style.transition = "opacity 0.3s ease"

            // Add HTML to the wrapper
            contentWrapper.innerHTML = contentToDisplay.html

            // Add the wrapper to the content container
            contentRef.current.appendChild(contentWrapper)

            // Fade in the content
            setTimeout(() => {
              contentWrapper.style.opacity = "1"
            }, 50)

            // Execute JavaScript with proper error handling
            if (contentToDisplay.js) {
              try {
                // Clean up previous carousel scripts
                const existingScripts = document.querySelectorAll("[data-carousel-script]")
                existingScripts.forEach((script) => script.remove())

                // Create a new script element to execute the JS
                const scriptElement = document.createElement("script")

                // Inject margin variables into the JavaScript
                let processedJS = contentToDisplay.js
                processedJS = `
                  // Carousel margin variables (using window to avoid redeclaration)
                  window.carouselMargins = {
                    top: '${top}',
                    bottom: '${bottom}',
                    left: '${left}',
                    right: '${right}'
                  };
                  window.contentMargins = {
                    top: '${contentTop}',
                    bottom: '${contentBottom}',
                    left: '${contentLeft}',
                    right: '${contentRight}'
                  };
                  // Also create a local reference for convenience
                  const carouselMargins = window.carouselMargins;
                  const contentMargins = window.contentMargins;
                  ${processedJS}
                `

                scriptElement.textContent = processedJS
                scriptElement.setAttribute("data-carousel-script", "true")
                contentRef.current.appendChild(scriptElement)
              } catch (error) {
                console.error("Error executing loaded JavaScript:", error)
              }
            }
          }

          // Fade in the content container
          if (contentRef.current) {
            contentRef.current.style.opacity = "1"
          }

          setSelectedCard(slideIndex)
          setIsContentVisible(true)
        }, 150) // Wait for fade out to complete
      })
    },
    [
      loadedContent,
      top,
      bottom,
      left,
      right,
      contentTop,
      contentBottom,
      contentLeft,
      contentRight,
      memoizedSlides,
      isTransitioning,
    ],
  )

  // Optimized content loading with better error handling
  const loadSlideContent = useCallback(
    async (slideIndex: number) => {
      const slide = memoizedSlides[slideIndex]
      if (!slide || isTransitioning) return

      setIsLoading(true)

      try {
        if (slide.contentFile && !loadedContent[slideIndex]) {
          // Load content if not already loaded
          const response = await fetch(slide.contentFile)
          if (response.ok) {
            const html = await response.text()

            // Extract CSS and JS from the HTML
            const cssMatches = html.match(/<style[^>]*>([\s\S]*?)<\/style>/gi) || []
            const jsMatches = html.match(/<script[^>]*>([\s\S]*?)<\/script>/gi) || []

            const css = cssMatches.map((match) => match.replace(/<\/?style[^>]*>/gi, "")).join("\n")
            const js = jsMatches.map((match) => match.replace(/<\/?script[^>]*>/gi, "")).join("\n")

            // Remove style and script tags from HTML
            const cleanHtml = html
              .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, "")
              .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, "")

            const newLoadedContent = { ...loadedContent }
            newLoadedContent[slideIndex] = { html: cleanHtml, css, js }
            setLoadedContent(newLoadedContent)

            displayContent(slideIndex, newLoadedContent[slideIndex])
          } else {
            console.error(`Failed to load content for slide ${slideIndex}:`, response.status)
          }
        } else if (slide.contentComponent) {
          // Directly display React component
          displayContent(slideIndex)
        } else {
          // Display preloaded content
          displayContent(slideIndex, loadedContent[slideIndex])
        }
      } catch (error) {
        console.error(`Error loading content for slide ${slideIndex}:`, error)
      } finally {
        setIsLoading(false)
      }
    },
    [memoizedSlides, loadedContent, displayContent, isTransitioning],
  )

  // Optimized content hiding with smooth transitions
  const hideContent = useCallback(() => {
    if (!contentRef.current) return

    // Fade out content
    contentRef.current.style.opacity = "0"

    setIsContentVisible(false)

    setTimeout(() => {
      setSelectedCard(null)

      // Cleanup all React roots with better error handling
      const rootsToCleanup = Array.from(reactRoots.current.entries())
      reactRoots.current.clear()

      // Clean up roots with proper error handling
      rootsToCleanup.forEach(([slideIndex, root]) => {
        setTimeout(() => {
          try {
            root.unmount()
          } catch (error) {
            console.warn(`Error unmounting root for slide ${slideIndex}:`, error)
          }
        }, 0)
      })

      if (contentRef.current) {
        contentRef.current.innerHTML = ""
      }

      // Clean up styles and scripts
      const existingStyles = document.querySelectorAll("[data-carousel-style]")
      existingStyles.forEach((style) => style.remove())
      const existingScripts = document.querySelectorAll("[data-carousel-script]")
      existingScripts.forEach((script) => script.remove())
    }, 300) // Wait for fade out animation
  }, [])

  // Optimized card click handler
  const handleCardClick = useCallback(
    (slideIndex: number) => {
      if (isTransitioning) return // Prevent clicks during transitions

      if (slideIndex === activeIndex && selectedCard === slideIndex) {
        // If same card is clicked again, scroll down smoothly
        window.scrollBy({
          top: 600,
          behavior: "smooth",
        })
      }
    },
    [activeIndex, selectedCard, isTransitioning],
  )

  // Preload content with better performance
  const preloadContent = useCallback(async () => {
    const contentPromises = memoizedSlides.map(async (slide, index) => {
      // Skip React components as they don't need preloading
      if (slide.contentComponent) return null

      if (slide.contentFile && !loadedContent[index]) {
        try {
          const response = await fetch(slide.contentFile)
          if (response.ok) {
            const html = await response.text()

            // Extract CSS and JS from the HTML
            const cssMatches = html.match(/<style[^>]*>([\s\S]*?)<\/style>/gi) || []
            const jsMatches = html.match(/<script[^>]*>([\s\S]*?)<\/script>/gi) || []

            const css = cssMatches.map((match) => match.replace(/<\/?style[^>]*>/gi, "")).join("\n")
            const js = jsMatches.map((match) => match.replace(/<\/?script[^>]*>/gi, "")).join("\n")

            // Remove style and script tags from HTML
            const cleanHtml = html
              .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, "")
              .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, "")

            return { index, content: { html: cleanHtml, css, js } }
          }
        } catch (error) {
          console.error(`Failed to preload content for slide ${index}:`, error)
        }
      }
      return null
    })

    const results = await Promise.all(contentPromises)
    const newLoadedContent = { ...loadedContent }

    results.forEach((result) => {
      if (result) {
        newLoadedContent[result.index] = result.content
      }
    })

    setLoadedContent(newLoadedContent)
  }, [memoizedSlides, loadedContent])

  // Enhanced Glide.js initialization with better event handling
  const initializeGlide = useCallback(() => {
    if (typeof window !== "undefined" && window.Glide && !glideInitialized.current && glideRef.current) {
      const glide = new window.Glide(glideRef.current, glideOptions)

      // Enhanced event handlers with transition management
      glide.on("run.before", () => {
        setIsTransitioning(true)

        // Clear any pending content updates
        if (contentUpdateTimeoutRef.current) {
          clearTimeout(contentUpdateTimeoutRef.current)
        }
      })

      glide.on("run.after", () => {
        const newActiveIndex = glide.index
        lastActiveIndexRef.current = newActiveIndex
        setActiveIndex(newActiveIndex)

        // Clear transition timeout
        if (transitionTimeoutRef.current) {
          clearTimeout(transitionTimeoutRef.current)
        }

        // Set transition complete after animation duration
        transitionTimeoutRef.current = setTimeout(() => {
          setIsTransitioning(false)

          // Update content after transition is complete
          debouncedContentUpdate(newActiveIndex)
        }, glideOptions.animationDuration + 50)
      })

      glide.on("run", () => {
        const newActiveIndex = glide.index
        setActiveIndex(newActiveIndex)
      })

      glide.mount()

      // Load initial slide content
      const initialIndex = glide.index
      setActiveIndex(initialIndex)
      lastActiveIndexRef.current = initialIndex

      // Load initial content with delay to ensure smooth startup
      setTimeout(() => {
        const initialSlide = memoizedSlides[initialIndex]
        if (initialSlide?.contentFile || initialSlide?.contentComponent) {
          loadSlideContent(initialIndex)
        }
      }, 200)

      glideInitialized.current = true
      glideInstance.current = glide

      return () => {
        // Enhanced cleanup
        if (transitionTimeoutRef.current) {
          clearTimeout(transitionTimeoutRef.current)
        }
        if (contentUpdateTimeoutRef.current) {
          clearTimeout(contentUpdateTimeoutRef.current)
        }

        glide.destroy()
        glideInitialized.current = false
        glideInstance.current = null
      }
    }
  }, [memoizedSlides, loadSlideContent, debouncedContentUpdate])

  // Preload content on component mount with delay
  useEffect(() => {
    const timer = setTimeout(() => {
      preloadContent()
    }, 1000)

    return () => clearTimeout(timer)
  }, [preloadContent])

  // Initialize Glide when script loads
  useEffect(() => {
    return initializeGlide()
  }, [initializeGlide])

  // Cleanup effect for React roots and timeouts
  useEffect(() => {
    return () => {
      // Clear timeouts
      if (transitionTimeoutRef.current) {
        clearTimeout(transitionTimeoutRef.current)
      }
      if (contentUpdateTimeoutRef.current) {
        clearTimeout(contentUpdateTimeoutRef.current)
      }

      // Cleanup all React roots on component unmount
      const rootsToCleanup = Array.from(reactRoots.current.values())
      reactRoots.current.clear()

      rootsToCleanup.forEach((root) => {
        setTimeout(() => {
          try {
            root.unmount()
          } catch (error) {
            console.warn("Error unmounting root during cleanup:", error)
          }
        }, 0)
      })
    }
  }, [])

  return (
    <>
      <Script
        src="https://unpkg.com/@glidejs/glide@3.7.1/dist/glide.min.js"
        strategy="afterInteractive"
        onLoad={initializeGlide}
      />

      <style jsx global>{`
        .glide {
          position: relative;
          width: 100%;
          box-sizing: border-box;
          padding: 0;
          margin: 0;
        }
        
        .glide * {
          box-sizing: inherit;
        }
        
        .glide__track {
          overflow: visible;
          width: 100%;
        }
        
        .glide__slides {
          position: relative;
          width: 100%;
          list-style: none;
          backface-visibility: hidden;
          transform-style: preserve-3d;
          touch-action: pan-Y;
          overflow: visible;
          margin: 0;
          padding: 0;
          white-space: nowrap;
          display: flex;
          flex-wrap: nowrap;
          will-change: transform;
        }
        
        .glide__slides--dragging {
          user-select: none;
        }
        
        .glide__slide {
          border-radius: 0.5rem;
          height: 100%;
          width: 70vw;
          flex-shrink: 0;
          white-space: normal;
          user-select: none;
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: transparent;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          transform: translateZ(0); /* Force GPU acceleration */
          will-change: transform;
        }
        
        .glide__slide a {
          user-select: none;
          -webkit-user-drag: none;
          -moz-user-select: none;
          -ms-user-select: none;
        }
        
        .glide__arrows {
          -webkit-touch-callout: none;
          user-select: none;
        }

        img {
          display: block;
          max-width: 100%;
          height: auto;
        }

        .carousel-card {
          width: 100%;
          height: calc(70vw * 9 / 16);
          max-height: 70vh;
          border-radius: 0.5rem;
          overflow: hidden;
          position: relative;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          transform: translateZ(0); /* Force GPU acceleration */
          will-change: transform;
        }

        .carousel-card img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 0.5rem;
          transform: translateZ(0); /* Force GPU acceleration */
        }

        .carousel-wrapper {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 100%;
          max-width: 100vw;
          margin: 0;
          padding: 0;
          transform: translateZ(0); /* Force GPU acceleration */
        }

        .carousel-container {
          overflow-x: hidden;
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          transform: translateZ(0); /* Force GPU acceleration */
          padding: 0;
          margin: 0;
        }

        .glide__slide:not(:last-child) {
          margin-right: 6px !important;
        }

        .glide__slides {
          gap: 0;
        }

        .content-container {
          width: 100%;
          max-width: 100vw;
          margin-top: 2rem;
          opacity: 0;
          transform: translateY(20px) translateZ(0);
          transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          pointer-events: none;
          will-change: transform, opacity;
        }

        .content-container.visible {
          opacity: 1;
          transform: translateY(0) translateZ(0);
          pointer-events: auto;
        }

        .content-wrapper {
          background: white;
          border-radius: 1rem;
          overflow: hidden;
          position: relative;
          transform: translateZ(0); /* Force GPU acceleration */
        }

        .content-header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 1rem 2rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .content-body {
          padding: 2rem;
          min-height: 200px;
          transform: translateZ(0); /* Force GPU acceleration */
        }

        .loading-spinner {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 200px;
        }

        .spinner {
          width: 40px;
          height: 40px;
          border: 4px solid #f3f3f3;
          border-top: 4px solid #667eea;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .close-button {
          background: rgba(255, 255, 255, 0.2);
          border: none;
          color: white;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: background 0.2s ease;
        }

        .close-button:hover {
          background: rgba(255, 255, 255, 0.3);
        }

        .card-content-wrapper {
          box-sizing: border-box;
          width: 100%;
          transform: translateZ(0); /* Force GPU acceleration */
        }

        .react-component-wrapper {
          box-sizing: border-box;
          width: 100%;
          transform: translateZ(0); /* Force GPU acceleration */
        }

        .html-content-wrapper {
          box-sizing: border-box;
          width: 100%;
          transform: translateZ(0); /* Force GPU acceleration */
        }

        .component-error-boundary {
          transform: translateZ(0); /* Force GPU acceleration */
        }

        /* Smooth transitions for all carousel elements */
        .glide__slide,
        .carousel-card,
        .content-container {
          backface-visibility: hidden;
          perspective: 1000px;
        }

        /* Prevent layout shifts during transitions */
        .glide__track {
          contain: layout style paint;
        }

        /* Optimize rendering performance */
        .carousel-wrapper * {
          box-sizing: border-box;
        }
      `}</style>

      <div
        className="carousel-wrapper"
        style={{
          marginTop: top,
          marginBottom: bottom,
        }}
      >
        <div className="carousel-container">
          <div className="glide py-6" ref={glideRef}>
            <div className="glide__track" data-glide-el="track">
              <ul className="glide__slides">
                {memoizedSlides.map((slide, index) => (
                  <li key={slide.id} className="glide__slide">
                    <div
                      className="carousel-card"
                      onClick={() => handleCardClick(index)}
                      style={{
                        pointerEvents: isTransitioning ? "none" : "auto",
                      }}
                    >
                      <img src={slide.image || "/placeholder.svg"} alt={slide.title} draggable={false} />
                    </div>
                  </li>
                ))}
              </ul>
            </div>

            <div className="glide__arrows" data-glide-el="controls">
              <button
                className="glide__arrow glide__arrow--left absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-3 shadow-lg z-10 transition-all duration-200"
                data-glide-dir="<"
                aria-label="Previous slide"
                disabled={isTransitioning}
                style={{
                  pointerEvents: isTransitioning ? "none" : "auto",
                  opacity: isTransitioning ? 0.5 : 1,
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polyline points="15 18 9 12 15 6"></polyline>
                </svg>
              </button>
              <button
                className="glide__arrow glide__arrow--right absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-3 shadow-lg z-10 transition-all duration-200"
                data-glide-dir=">"
                aria-label="Next slide"
                disabled={isTransitioning}
                style={{
                  pointerEvents: isTransitioning ? "none" : "auto",
                  opacity: isTransitioning ? 0.5 : 1,
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Dynamic Content Container with improved transitions */}
        <div className={`content-container ${isContentVisible ? "visible" : ""}`}>
          <div className="content-wrapper">
            <div className="content-body">
              {isLoading ? (
                <div className="loading-spinner">
                  <div className="spinner"></div>
                </div>
              ) : (
                <div ref={contentRef} style={{ opacity: 1, transition: "opacity 0.3s ease" }}></div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
