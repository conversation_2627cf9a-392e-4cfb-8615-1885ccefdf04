"use client"

import { Trees } from "lucide-react"

interface ForestComponentProps {
  title?: string
}

export default function ForestComponent({ title = "Forest Path" }: ForestComponentProps) {
  return (
    <div className="w-full max-w-3xl mx-auto p-6 bg-gradient-to-b from-green-100 to-emerald-50 rounded-lg shadow-md">
      <div className="flex items-center justify-center mb-6">
        <Trees className="h-16 w-16 text-emerald-700" />
      </div>
      <h2 className="text-3xl font-bold text-center text-emerald-800 mb-4">{title}</h2>
      <p className="text-xl text-emerald-700 text-center">
        A serene pathway winding through ancient trees, dappled with sunlight filtering through the canopy.
      </p>
    </div>
  )
}
