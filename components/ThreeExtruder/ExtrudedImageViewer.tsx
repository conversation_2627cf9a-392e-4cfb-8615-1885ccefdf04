import React, { useState, useEffect, useRef, useMemo, Suspense } from 'react';
import { Canvas, useThree, useFrame, useLoader } from '@react-three/fiber';
import { OrbitControls, Environment, Stats } from '@react-three/drei';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import * as THREE from 'three';
import ExtrudedImageMesh from './ExtrudedImageMesh';
import { EffectComposer, BrightnessContrast } from '@react-three/postprocessing';
import { AsciiEffect } from './effects/AsciiEffect';
import { ExtrudedImageOptions, ExtrusionPreset } from './types';

interface ExtrudedImageViewerProps {
  defaultPresetName?: string;
  hideControlsPanel?: boolean;
  defaultUseAsciiFilter?: boolean;
}

interface GltfModelRendererProps {
  src: string;
  scale?: number | [number, number, number];
  pingPongLoop?: boolean;
  glbUseSolidColor?: boolean;
  glbSolidColor?: string;
}

const GltfModelRenderer: React.FC<GltfModelRendererProps> = ({ 
  src, 
  scale = 1, 
  pingPongLoop = false,
  glbUseSolidColor = false,
  glbSolidColor = '#ffffff'
}) => {
  const gltf = useLoader(GLTFLoader, src);
  const mixer = useRef<THREE.AnimationMixer | null>(null);
  const isPlayingForward = useRef(true);

  useEffect(() => {
    if (gltf.scene && gltf.animations && gltf.animations.length > 0) {
      mixer.current = new THREE.AnimationMixer(gltf.scene);
      const animationAction = mixer.current.clipAction(gltf.animations[0]); // Assuming first animation

      if (pingPongLoop) {
        animationAction.setLoop(THREE.LoopOnce, 1);
        isPlayingForward.current = true; // Ensure it starts forward
        animationAction.timeScale = 1; // Ensure it starts forward

        const onFinished = (event: any) => {
          if (event.action === animationAction) { // Ensure it's our action
            if (isPlayingForward.current) {
              animationAction.timeScale = -1;
              // animationAction.time = animationAction.getClip().duration; // Start from end
              animationAction.paused = false; // Ensure it's not paused from previous stop
              animationAction.play();
              isPlayingForward.current = false;
            } else {
              animationAction.timeScale = 1;
              // animationAction.time = 0; // Start from beginning
              animationAction.paused = false;
              animationAction.play();
              isPlayingForward.current = true;
            }
          }
        };
        mixer.current.addEventListener('finished', onFinished);
        animationAction.play();
        
        return () => {
          mixer.current?.removeEventListener('finished', onFinished);
          mixer.current?.stopAllAction();
          animationAction.stop();
        };
      } else {
        // Default behavior: loop all animations
        gltf.animations.forEach(clip => {
          if(mixer.current){
            const action = mixer.current.clipAction(clip);
            action.setLoop(THREE.LoopRepeat, Infinity); // Explicitly set repeat for non-pingpong
            action.play();
          }
        });
        return () => {
          mixer.current?.stopAllAction();
        };
      }
    }
  }, [gltf, pingPongLoop]);

  // Effect to apply solid color to GLB materials
  useEffect(() => {
    if (!gltf.scene) return;

    gltf.scene.traverse((object) => {
      if ((object as THREE.Mesh).isMesh) {
        const mesh = object as THREE.Mesh;
        if (glbUseSolidColor) {
          // Store original material if not already stored
          if (!mesh.userData.originalMaterial) {
            mesh.userData.originalMaterial = mesh.material;
          }
          // Apply new solid color material
          const solidMaterial = new THREE.MeshStandardMaterial({
            color: glbSolidColor,
            metalness: (mesh.material as THREE.MeshStandardMaterial).metalness !== undefined ? (mesh.material as THREE.MeshStandardMaterial).metalness : 0.5,
            roughness: (mesh.material as THREE.MeshStandardMaterial).roughness !== undefined ? (mesh.material as THREE.MeshStandardMaterial).roughness : 0.5,
          });
          mesh.material = solidMaterial;
        } else {
          // Revert to original material if it exists
          if (mesh.userData.originalMaterial) {
            mesh.material = mesh.userData.originalMaterial;
          }
        }
      }
    });

    // Cleanup: If the component unmounts or gltf.scene changes, ensure original materials are restored if they were changed
    return () => {
      if (gltf.scene && glbUseSolidColor) { // Only revert if we actually applied solid color
        gltf.scene.traverse((object) => {
          if ((object as THREE.Mesh).isMesh) {
            const mesh = object as THREE.Mesh;
            if (mesh.userData.originalMaterial) {
              mesh.material = mesh.userData.originalMaterial;
              // Optionally clear userData: delete mesh.userData.originalMaterial;
            }
          }
        });
      }
    };
  }, [gltf, glbUseSolidColor, glbSolidColor]);

  useFrame((state, delta) => {
    mixer.current?.update(delta);
  });

  return <primitive object={gltf.scene} scale={scale} />;
};

const CameraRefSetter: React.FC<{
  onCameraReady: (camera: THREE.PerspectiveCamera) => void;
}> = ({ onCameraReady }) => {
  const { camera } = useThree();
  useEffect(() => {
    // Ensure camera is a PerspectiveCamera, which has an fov property
    if (camera instanceof THREE.PerspectiveCamera) {
      onCameraReady(camera);
    }
  }, [camera, onCameraReady]);
  return null; // This component doesn't render anything visible
};

const ExtrudedImageViewer: React.FC<ExtrudedImageViewerProps> = (props) => {
  const presets: ExtrusionPreset[] = [
    {
      name: "Blender",
      imageSrc: "https://upload.wikimedia.org/wikipedia/commons/thumb/0/0c/Blender_logo_no_text.svg/939px-Blender_logo_no_text.svg.png",
      brightness: 1,
      sizeX: 0.85,
      pixelationFactor: 8,
      sideColor: "#C64600",
      thickness: 0.1,
      cameraPosition: [0.13, 0.07, 1.10],
      fov: 60,
    },
    {
      name: "Unity",
      imageSrc: "https://i.ibb.co/BVN8z7yF/unity.png",
      brightness: 1.5,
      sizeX: 0.75,
      pixelationFactor: 4,
      sideColor: "#3D3846",
      thickness: 0.1,
    },
    {
      name: "Unreal",
      imageSrc: "https://i.ibb.co/LDbk8b4z/unreal.webp",
      brightness: 2,
      sizeX: 0.75,
      pixelationFactor: 2,
      sideColor: "#3D3846",
      thickness: 0.05,
    },
    {
      name: "Solid Duck",
      glbSrc: "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/Duck/glTF-Binary/Duck.glb",
      sizeX: 1, // GLB models might need different default scaling
      glbUseSolidColor: true,
      glbSolidColor: "#1E90FF", // Dodger Blue
      cameraPosition: [0.5, 0.5, 1.5],
      fov: 75,
      // GLB Lighting for this preset
      glbAmbientLightIntensity: 0.7,
      glbDirectionalLightIntensity: 1.0,
      glbDirectionalLightColor: '#FFFFFF',
      glbDirectionalLightPosition: [2, 3, 4],
      // Scene post-processing for this preset
      sceneBrightness: 0.1,
      sceneContrast: 0.05,
    },
    {
      name: "Lock",
      glbSrc: "https://raw.githubusercontent.com/SurrealSystems/Surreal/refs/heads/main/lock.glb",
      sizeX: 1, // GLB models might need different default scaling
      glbUseSolidColor: true,
      glbSolidColor: "#F6F5F4",
      cameraPosition: [1.98, 0.18, 0.14],
      fov: 75,
      // GLB Lighting for this preset
      glbAmbientLightIntensity: 0.7,
      glbDirectionalLightIntensity: 1.0,
      glbDirectionalLightColor: '#FFFFFF',
      glbDirectionalLightPosition: [2, 3, 4],
      // Scene post-processing for this preset
      sceneBrightness: 0.1,
      sceneContrast: 0.05,
    },
  ];

  const [imageSrc, setImageSrc] = useState<string | undefined>(props.defaultPresetName ? undefined : '/three-extruded-assets/logo.png');
  const [gifBuffer, setGifBuffer] = useState<ArrayBuffer | undefined>();
  const [glbSrc, setGlbSrc] = useState<string | undefined>(undefined);
  const [options, setOptions] = useState<ExtrudedImageOptions>({
    thickness: 0.1,
    sizeX: 0.75,
    sizeY: 0.75,
    alphaThreshold: 128,
    legacy: false,
    animationSpeed: 10,
    pixelationFactor: 1,
    useAsciiFilter: props.defaultUseAsciiFilter ?? false,
    asciiCharacters: ` .:,'-^=*+?!|0#X%WM@`,
    asciiFontSize: 68,
    asciiCellSize: 13,
    asciiColor: '#ffffff',
    asciiBackgroundColor: '#000000',
    asciiInvert: false,
    blackThreshold: 255,
    brightness: 1.0,
    glbPingPongLoop: false, // Added for GLB animation control
    // GLB Lighting Defaults
    glbAmbientLightIntensity: 0.5,
    glbDirectionalLightIntensity: 0.8,
    glbDirectionalLightColor: '#ffffff',
    glbDirectionalLightPosition: [5, 5, 5],
    // Scene Post-Processing Defaults
    sceneBrightness: 0,
    sceneContrast: 0,
    // GLB Solid Color Defaults
    glbUseSolidColor: false,
    glbSolidColor: '#ffffff',
  });
  const [sideColor, setSideColor] = useState<string>('#000000');
  const [showAdvancedControls, setShowAdvancedControls] = useState(false);
  const [dynamicCameraPosition, setDynamicCameraPosition] = useState<[number, number, number]>([1.5, 1.5, 2.5]);
  const [dynamicCameraFov, setDynamicCameraFov] = useState<number>(75);
  const [currentColorScheme, setCurrentColorScheme] = useState<'light' | 'dark'>('light');
  const [glbUrlInput, setGlbUrlInput] = useState<string>('');
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null); // State for GLB URL input

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleLoadGlbFromUrl = () => {
    if (glbUrlInput.trim() === '') {
      alert('Please enter a GLB URL.');
      return;
    }
    setGlbSrc(glbUrlInput.trim());
    setImageSrc(undefined);
    setGifBuffer(undefined);
    // Optionally, clear the input field after loading
    // setGlbUrlInput(''); 
  };

  const applyPreset = (preset: ExtrusionPreset) => {
    if (preset.glbSrc) {
      setGlbSrc(preset.glbSrc);
      setImageSrc(undefined);
      setGifBuffer(undefined);
      setGlbUrlInput(preset.glbSrc); // Update input field to reflect preset GLB URL
    } else if (preset.imageSrc) {
      setGlbSrc(undefined);
      setImageSrc(preset.imageSrc);
      setGifBuffer(undefined);
      setGlbUrlInput(''); // Clear GLB URL input if it's an image preset
    } else {
      // If neither glbSrc nor imageSrc is in the preset, clear both
      setGlbSrc(undefined);
      setImageSrc(undefined);
      setGifBuffer(undefined);
      setGlbUrlInput('');
    }
    setOptions(prevOptions => ({
      ...prevOptions,
      thickness: preset.thickness ?? prevOptions.thickness,
      sizeX: preset.sizeX ?? prevOptions.sizeX,
      pixelationFactor: preset.pixelationFactor ?? prevOptions.pixelationFactor,
      brightness: preset.brightness ?? prevOptions.brightness,
      // Apply GLB lighting from preset if available
      glbAmbientLightIntensity: preset.glbAmbientLightIntensity ?? prevOptions.glbAmbientLightIntensity,
      glbDirectionalLightIntensity: preset.glbDirectionalLightIntensity ?? prevOptions.glbDirectionalLightIntensity,
      glbDirectionalLightColor: preset.glbDirectionalLightColor ?? prevOptions.glbDirectionalLightColor,
      glbDirectionalLightPosition: preset.glbDirectionalLightPosition ?? prevOptions.glbDirectionalLightPosition,
      // Apply Scene Post-Processing from preset if available
      sceneBrightness: preset.sceneBrightness ?? prevOptions.sceneBrightness,
      sceneContrast: preset.sceneContrast ?? prevOptions.sceneContrast,
      // Apply GLB Solid Color from preset if available
      glbUseSolidColor: preset.glbUseSolidColor ?? prevOptions.glbUseSolidColor,
      glbSolidColor: preset.glbSolidColor ?? prevOptions.glbSolidColor,
    }));
    setSideColor(preset.sideColor ?? '#000000');
    if (preset.cameraPosition) setDynamicCameraPosition(preset.cameraPosition);
    if (preset.fov) setDynamicCameraFov(preset.fov);
  };

  useEffect(() => {
    if (props.defaultPresetName) {
      const presetToApply = presets.find(p => p.name === props.defaultPresetName);
      if (presetToApply && !glbSrc) {
        applyPreset(presetToApply);
      }
    }
  }, [props.defaultPresetName, glbSrc]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      const reader = new FileReader();
      const fileExtension = file.name.split('.').pop()?.toLowerCase();

      if (fileExtension === 'glb') {
        reader.onload = (e) => {
          if (e.target?.result && typeof e.target.result === 'string') {
            setGlbSrc(e.target.result as string);
            setImageSrc(undefined);
            setGifBuffer(undefined);
          }
        };
        reader.readAsDataURL(file);
      } else if (file.type.startsWith('image/')) {
        if (file.type === 'image/gif') {
          reader.onload = (e) => {
            if (e.target?.result instanceof ArrayBuffer) {
              setGifBuffer(e.target.result);
              setImageSrc(undefined);
              setGlbSrc(undefined);
            }
          };
          reader.readAsArrayBuffer(file);
        } else {
          reader.onload = (e) => {
            if (e.target?.result && typeof e.target.result === 'string') {
              setImageSrc(e.target.result as string);
              setGifBuffer(undefined);
              setGlbSrc(undefined);
            }
          };
          reader.readAsDataURL(file);
        }
      } else {
        alert('Unsupported file type. Please upload an image (PNG, JPG, GIF) or a GLB model.');
      }
    }
  };

  const handleOptionChange = (param: keyof ExtrudedImageOptions, value: any) => {
    setOptions(prev => ({ ...prev, [param]: value }));
  };

  const asciiEffect = useMemo(() => {
    if (!options.useAsciiFilter) return null;
    const isDarkMode = currentColorScheme === 'dark';
    const modeAsciiColor = options.asciiColor && options.asciiColor !== '#ffffff' ? options.asciiColor : (isDarkMode ? '#FFFFFF' : '#000000');
    const modeAsciiBackgroundColor = options.asciiBackgroundColor && options.asciiBackgroundColor !== '#000000' ? options.asciiBackgroundColor : (isDarkMode ? '#000000' : '#FFFFFF');

    return new AsciiEffect({
      characters: options.asciiCharacters,
      fontSize: options.asciiFontSize,
      cellSize: options.asciiCellSize,
      color: modeAsciiColor,
      backgroundColor: modeAsciiBackgroundColor,
      invert: options.asciiInvert,
    });
  }, [
    options.useAsciiFilter,
    options.asciiCharacters,
    options.asciiFontSize,
    options.asciiCellSize,
    options.asciiColor,
    options.asciiBackgroundColor,
    options.asciiInvert,
    currentColorScheme,
    glbSrc
  ]);

  // Prepare active effects for the EffectComposer
  const activeEffectNodes: React.ReactElement[] = [];
  if (options.useAsciiFilter && asciiEffect) {
    activeEffectNodes.push(<primitive key="ascii" object={asciiEffect} />);
  }
  if (options.sceneBrightness !== 0 || options.sceneContrast !== 0) {
    activeEffectNodes.push(
      <BrightnessContrast
        key="brightnessContrast"
        brightness={options.sceneBrightness}
        contrast={options.sceneContrast}
      />
    );
  }

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = () => setCurrentColorScheme(mediaQuery.matches ? 'dark' : 'light');
    handleChange();
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const isImageMode = imageSrc || gifBuffer;

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100vh', background: currentColorScheme === 'dark' ? '#121212' : '#f0f0f0', color: currentColorScheme === 'dark' ? '#e0e0e0' : '#333' }}>
      {!props.hideControlsPanel && (
        <div style={{ padding: '10px', borderBottom: `1px solid ${currentColorScheme === 'dark' ? '#333' : '#ccc'}`, background: currentColorScheme === 'dark' ? '#1e1e1e' : '#fff' }}>
          <input type="file" ref={fileInputRef} onChange={handleFileChange} accept="image/*,.glb" style={{ marginBottom: '10px' }} />
          {/* GLB from URL input */}
          <div style={{ marginBottom: '10px', display: 'flex', alignItems: 'center' }}>
            <input 
              type="text" 
              placeholder="Enter GLB URL" 
              value={glbUrlInput} 
              onChange={(e) => setGlbUrlInput(e.target.value)} 
              style={{ marginRight: '5px', padding: '5px', flexGrow: 1, background: currentColorScheme === 'dark' ? '#333' : '#fff', color: currentColorScheme === 'dark' ? '#e0e0e0' : '#333', border: `1px solid ${currentColorScheme === 'dark' ? '#555' : '#ccc'}` }}
            />
            <button onClick={handleLoadGlbFromUrl} style={{ padding: '5px 10px' }}>Load GLB from URL</button>
          </div>
          <div style={{ marginBottom: '10px' }}>
            <button 
              onClick={() => {
                if (cameraRef.current) {
                  const pos = cameraRef.current.position.toArray().map(v => parseFloat(v.toFixed(2)));
                  const fov = parseFloat(cameraRef.current.fov.toFixed(2));
                  console.log(`Camera Info for Preset:\ncameraPosition: [${pos[0]}, ${pos[1]}, ${pos[2]}],\nfov: ${fov},`);
                } else {
                  console.log('Camera reference not yet available.');
                }
              }}
              style={{ marginRight: '10px' }}
            >
              Print Camera Info
            </button>
            Presets: {' '}
            {presets.map(p => (
              <button key={p.name} onClick={() => applyPreset(p)} style={{ marginRight: '5px' }}>{p.name}</button>
            ))}
          </div>
          <button onClick={() => setShowAdvancedControls(!showAdvancedControls)} style={{ marginBottom: '10px' }}>
            {showAdvancedControls ? 'Hide' : 'Show'} Controls
          </button>
          {showAdvancedControls && (
            <>
              {isImageMode && (
                <>
                  <div><label>Brightness: <input type="range" min="0.1" max="3" step="0.1" value={options.brightness} onChange={(e) => handleOptionChange('brightness', parseFloat(e.target.value))} /></label></div>
                  <div><label>Thickness: <input type="range" min="0.01" max="0.5" step="0.01" value={options.thickness} onChange={(e) => handleOptionChange('thickness', parseFloat(e.target.value))} /></label></div>
                  <div><label>Size X: <input type="range" min="0.1" max="2" step="0.01" value={options.sizeX} onChange={(e) => handleOptionChange('sizeX', parseFloat(e.target.value))} /></label></div>
                  <div><label>Size Y: <input type="range" min="0.1" max="2" step="0.01" value={options.sizeY} onChange={(e) => handleOptionChange('sizeY', parseFloat(e.target.value))} /></label></div>
                  <div><label>Alpha Threshold: <input type="range" min="0" max="255" step="1" value={options.alphaThreshold} onChange={(e) => handleOptionChange('alphaThreshold', parseInt(e.target.value))} /></label></div>
                  <div><label>Black Threshold: <input type="range" min="0" max="255" step="1" value={options.blackThreshold} onChange={(e) => handleOptionChange('blackThreshold', parseInt(e.target.value))} /></label></div>
                  <div><label><input type="checkbox" checked={options.legacy} onChange={(e) => handleOptionChange('legacy', e.target.checked)} /> Use Legacy Mode</label></div>
                  <div style={{ marginTop: '5px' }}><label>Side Face Color: <input type="color" value={sideColor} onChange={(e) => setSideColor(e.target.value)} /></label></div>
                  <div><label>Pixelation: {options.pixelationFactor} <input type="range" min="1" max="8" step="1" value={options.pixelationFactor} onChange={(e) => handleOptionChange('pixelationFactor', parseInt(e.target.value, 10))} /></label></div>
                  {gifBuffer && (
                    <div><label>GIF Speed (fps): {options.animationSpeed} <input type="range" min="1" max="30" step="1" value={options.animationSpeed} onChange={(e) => handleOptionChange('animationSpeed', parseInt(e.target.value))} /></label></div>
                  )}
                </>
              )}
              {!isImageMode && glbSrc && (
                <>
                  <div>GLB model loaded. Image-specific controls are hidden.</div>
                  <div>
                    <label>
                      <input 
                        type="checkbox" 
                        checked={options.glbPingPongLoop} 
                        onChange={(e) => handleOptionChange('glbPingPongLoop', e.target.checked)} 
                      />
                      Ping-Pong Loop GLB Animation
                    </label>
                  </div>
                  {/* GLB Lighting Controls */}
                  <div style={{ marginTop: '10px', paddingTop: '10px', borderTop: `1px solid ${currentColorScheme === 'dark' ? '#333' : '#ccc'}` }}>
                    <strong>GLB Lighting:</strong>
                    <div>
                      <label>Ambient Intensity: {options.glbAmbientLightIntensity?.toFixed(2)}
                        <input type="range" min="0" max="2" step="0.1" value={options.glbAmbientLightIntensity}
                               onChange={(e) => handleOptionChange('glbAmbientLightIntensity', parseFloat(e.target.value))} />
                      </label>
                    </div>
                    <div>
                      <label>Directional Intensity: {options.glbDirectionalLightIntensity?.toFixed(2)}
                        <input type="range" min="0" max="2" step="0.1" value={options.glbDirectionalLightIntensity}
                               onChange={(e) => handleOptionChange('glbDirectionalLightIntensity', parseFloat(e.target.value))} />
                      </label>
                    </div>
                    <div>
                      <label>Directional Color: 
                        <input type="color" value={options.glbDirectionalLightColor}
                               onChange={(e) => handleOptionChange('glbDirectionalLightColor', e.target.value)} />
                      </label>
                    </div>
                    <div>
                      <label>Directional Pos X: {options.glbDirectionalLightPosition?.[0].toFixed(1)}
                        <input type="range" min="-10" max="10" step="0.5" value={options.glbDirectionalLightPosition?.[0]}
                               onChange={(e) => handleOptionChange('glbDirectionalLightPosition', [parseFloat(e.target.value), options.glbDirectionalLightPosition?.[1] ?? 0, options.glbDirectionalLightPosition?.[2] ?? 0])} />
                      </label>
                    </div>
                    <div>
                      <label>Directional Pos Y: {options.glbDirectionalLightPosition?.[1].toFixed(1)}
                        <input type="range" min="-10" max="10" step="0.5" value={options.glbDirectionalLightPosition?.[1]}
                               onChange={(e) => handleOptionChange('glbDirectionalLightPosition', [options.glbDirectionalLightPosition?.[0] ?? 0, parseFloat(e.target.value), options.glbDirectionalLightPosition?.[2] ?? 0])} />
                      </label>
                    </div>
                    <div>
                      <label>Directional Pos Z: {options.glbDirectionalLightPosition?.[2].toFixed(1)}
                        <input type="range" min="-10" max="10" step="0.5" value={options.glbDirectionalLightPosition?.[2]}
                               onChange={(e) => handleOptionChange('glbDirectionalLightPosition', [options.glbDirectionalLightPosition?.[0] ?? 0, options.glbDirectionalLightPosition?.[1] ?? 0, parseFloat(e.target.value)])} />
                      </label>
                    </div>
                  </div>
                  {/* GLB Solid Color Controls */}
                  <div style={{ marginTop: '10px', paddingTop: '10px', borderTop: `1px solid ${currentColorScheme === 'dark' ? '#333' : '#ccc'}` }}>
                    <strong>GLB Solid Color:</strong>
                    <div>
                      <label>
                        <input 
                          type="checkbox" 
                          checked={options.glbUseSolidColor} 
                          onChange={(e) => handleOptionChange('glbUseSolidColor', e.target.checked)} 
                        />
                        Use Solid Color for GLB
                      </label>
                    </div>
                    {options.glbUseSolidColor && (
                      <div>
                        <label>Solid Color: 
                          <input 
                            type="color" 
                            value={options.glbSolidColor}
                            onChange={(e) => handleOptionChange('glbSolidColor', e.target.value)} 
                          />
                        </label>
                      </div>
                    )}
                  </div>
                </>
              )}
              {/* ASCII Filter Controls - Applicable to both Image and GLB */}
              <div style={{ marginTop: '10px', paddingTop: '10px', borderTop: `1px solid ${currentColorScheme === 'dark' ? '#333' : '#ccc'}` }}>
                <div><label><input type="checkbox" checked={options.useAsciiFilter} onChange={(e) => handleOptionChange('useAsciiFilter', e.target.checked)} /> ASCII Filter</label></div>
                {/* Scene Brightness/Contrast Controls */}
                <div style={{ marginTop: '5px' }}>
                  <label>Scene Brightness: {options.sceneBrightness?.toFixed(2)}
                    <input type="range" min="-0.5" max="0.5" step="0.05" value={options.sceneBrightness}
                           onChange={(e) => handleOptionChange('sceneBrightness', parseFloat(e.target.value))} />
                  </label>
                </div>
                <div style={{ marginTop: '5px' }}>
                  <label>Scene Contrast: {options.sceneContrast?.toFixed(2)}
                    <input type="range" min="-0.5" max="0.5" step="0.05" value={options.sceneContrast}
                           onChange={(e) => handleOptionChange('sceneContrast', parseFloat(e.target.value))} />
                  </label>
                </div>
                {options.useAsciiFilter && (
                  <>
                    <div><label>ASCII Chars: <input type="text" value={options.asciiCharacters} onChange={(e) => handleOptionChange('asciiCharacters', e.target.value)} style={{width: '200px'}} /></label></div>
                    <div><label>Font Size: {options.asciiFontSize} <input type="range" min="8" max="128" step="1" value={options.asciiFontSize} onChange={(e) => handleOptionChange('asciiFontSize', parseInt(e.target.value,10))} /></label></div>
                    <div><label>Cell Size: {options.asciiCellSize} <input type="range" min="4" max="64" step="1" value={options.asciiCellSize} onChange={(e) => handleOptionChange('asciiCellSize', parseInt(e.target.value,10))} /></label></div>
                    <div><label>ASCII Color: <input type="color" value={options.asciiColor} onChange={(e) => handleOptionChange('asciiColor', e.target.value)} /></label></div>
                    <div><label>ASCII BG Color: <input type="color" value={options.asciiBackgroundColor} onChange={(e) => handleOptionChange('asciiBackgroundColor', e.target.value)} /></label></div>
                    <div><label><input type="checkbox" checked={options.asciiInvert} onChange={(e) => handleOptionChange('asciiInvert', e.target.checked)} /> Invert ASCII</label></div>
                  </>
                )}
              </div>
            </>
          )}
        </div>
      )}
      <div style={{ flexGrow: 1, position: 'relative' }}>
        <Canvas camera={{ position: dynamicCameraPosition, fov: dynamicCameraFov }}>
          <Suspense fallback={null}>
            <CameraRefSetter onCameraReady={(cam) => { cameraRef.current = cam; }} />
            {glbSrc && <GltfModelRenderer 
                    src={glbSrc} 
                    scale={0.5} 
                    pingPongLoop={options.glbPingPongLoop} 
                    glbUseSolidColor={options.glbUseSolidColor}
                    glbSolidColor={options.glbSolidColor}
                  />}
            {(imageSrc || gifBuffer) && !glbSrc && (
              <ExtrudedImageMesh
                imageSrc={imageSrc}
                gifBuffer={gifBuffer}
                options={options}
                sideColor={sideColor}
                onDimensionsChange={(dimensions: { width: number; height: number }) => {
                  console.log(`Image dimensions: ${dimensions.width}x${dimensions.height}`);
                }}
              />
            )}
          </Suspense>
          <OrbitControls />
          <Environment preset="sunset" />
          {/* GLB Specific Lighting */}
          {glbSrc && (
            <>
              <ambientLight intensity={options.glbAmbientLightIntensity} />
              <directionalLight 
                intensity={options.glbDirectionalLightIntensity} 
                color={options.glbDirectionalLightColor} 
                position={options.glbDirectionalLightPosition}
                castShadow // Optional: if you want shadows
              />
            </>
          )}
          {/* Effects: ASCII and/or Brightness/Contrast */}
          {activeEffectNodes.length > 0 ? (
            <EffectComposer>
              {activeEffectNodes}
            </EffectComposer>
          ) : null}
        </Canvas>
        <Stats />
      </div>
    </div>
  );
};

export default ExtrudedImageViewer;
