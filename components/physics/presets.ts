import { Preset } from './types';

const asciiArtPreset = {
  textOptions: {
    fontFamily: 'monospace',
    fontSize: 10,
    isBold: false,
    isItalic: false,
    letterSpacing: 1,
    color: 'auto',
  },
  collisionOptions: {
    color: 'auto',
    collisionBoxSizeX: 5,
    collisionBoxSizeY: 4,
  },
};

export const presets: Preset[] = [
  {
    name: 'Welcome',
    filename: 'welcome.txt',
    textOptions: {
      fontFamily: 'Arial',
      fontSize: 24,
      isBold: true,
      isItalic: false,
      letterSpacing: 0,
      color: 'auto',
    },
    collisionOptions: {
      color: '#FF5733',
    },
  },
  {
    name: 'A',
    filename: 'a.txt',
    ...asciiArtPreset,
  },
  {
    name: 'Ascii Cat',
    filename: 'asciicat.txt',
    ...asciiArtPreset,
  },
  {
    name: 'Ascii Dog',
    filename: 'asciidog.txt',
    ...asciiArtPreset,
  },
  {
    name: '<PERSON>cii <PERSON> Logo',
    filename: 'asciifulllogo.txt',
    ...asciiArtPreset,
  },
  {
    name: 'Ascii OK',
    filename: 'asciiok.txt',
    ...asciiArtPreset,
  },
  {
    name: 'Ascii What',
    filename: 'asciiwhat.txt',
    ...asciiArtPreset,
  },
  {
    name: 'E',
    filename: 'e.txt',
    ...asciiArtPreset,
  },
  {
    name: 'L',
    filename: 'l.txt',
    alt: 'lreplace.txt',
    ...asciiArtPreset,
  },
  {
    name: 'R',
    filename: 'r.txt',
    ...asciiArtPreset,
  },
  {
    name: 'R2',
    filename: 'r2.txt',
    ...asciiArtPreset,
  },
  {
    name: 'S',
    filename: 's.txt',
    ...asciiArtPreset,
  },
  {
    name: 'U',
    filename: 'u.txt',
    ...asciiArtPreset,
  },
];  // Add more presets here in the future
