"use client";

import React, { useEffect, useRef, useState } from 'react';

interface PerformanceStats {
  fps: number;
  frameTime: number;
  memoryUsage: number;
  bodyCount: number;
  renderTime: number;
}

interface PerformanceMonitorProps {
  engineRef: React.RefObject<Matter.Engine | null>;
  enabled?: boolean;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ 
  engineRef, 
  enabled = true 
}) => {
  const [stats, setStats] = useState<PerformanceStats>({
    fps: 0,
    frameTime: 0,
    memoryUsage: 0,
    bodyCount: 0,
    renderTime: 0,
  });
  
  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());
  const fpsHistory = useRef<number[]>([]);
  const renderStartTime = useRef(0);
  
  useEffect(() => {
    if (!enabled) return;
    
    let animationId: number;
    
    const updateStats = () => {
      const currentTime = performance.now();
      const deltaTime = currentTime - lastTime.current;
      
      frameCount.current++;
      
      // Calculate FPS every 60 frames for smoother display
      if (frameCount.current % 60 === 0) {
        const fps = Math.round(1000 / deltaTime);
        fpsHistory.current.push(fps);
        
        // Keep only last 10 FPS measurements for averaging
        if (fpsHistory.current.length > 10) {
          fpsHistory.current.shift();
        }
        
        const avgFps = Math.round(
          fpsHistory.current.reduce((a, b) => a + b, 0) / fpsHistory.current.length
        );
        
        // Get memory usage if available
        const memoryUsage = (performance as any).memory 
          ? Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024)
          : 0;
        
        // Get body count from Matter.js engine
        const bodyCount = engineRef.current 
          ? Matter.Composite.allBodies(engineRef.current.world).length
          : 0;
        
        setStats({
          fps: avgFps,
          frameTime: Math.round(deltaTime * 100) / 100,
          memoryUsage,
          bodyCount,
          renderTime: Math.round((currentTime - renderStartTime.current) * 100) / 100,
        });
      }
      
      lastTime.current = currentTime;
      renderStartTime.current = currentTime;
      animationId = requestAnimationFrame(updateStats);
    };
    
    animationId = requestAnimationFrame(updateStats);
    
    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [enabled, engineRef]);
  
  if (!enabled) return null;
  
  return (
    <div className="fixed top-4 right-4 bg-black/80 text-white p-3 rounded-lg font-mono text-sm z-50">
      <div className="space-y-1">
        <div className="flex justify-between gap-4">
          <span>FPS:</span>
          <span className={stats.fps < 30 ? 'text-red-400' : stats.fps < 50 ? 'text-yellow-400' : 'text-green-400'}>
            {stats.fps}
          </span>
        </div>
        <div className="flex justify-between gap-4">
          <span>Frame Time:</span>
          <span>{stats.frameTime}ms</span>
        </div>
        <div className="flex justify-between gap-4">
          <span>Bodies:</span>
          <span>{stats.bodyCount}</span>
        </div>
        {stats.memoryUsage > 0 && (
          <div className="flex justify-between gap-4">
            <span>Memory:</span>
            <span>{stats.memoryUsage}MB</span>
          </div>
        )}
        <div className="flex justify-between gap-4">
          <span>Render:</span>
          <span>{stats.renderTime}ms</span>
        </div>
      </div>
    </div>
  );
};

export default PerformanceMonitor;
