import { useTheme } from 'next-themes';
import { useEffect, useRef, useState } from 'react';
import Matter from 'matter-js';
import { TextOptions, BodyWithText } from './types';

interface ReplaceTrigger {
  presetId: number;
  altFilename: string;
}

interface ModeSwapTrigger {
  presetId: number;
  newMode: 'default' | 'character';
}

const getResolvedBackgroundColor = (color: string, theme: string | undefined) => {
  if (color === 'auto') {
    return 'transparent';
  }
  if (theme === 'dark') {
    if (color.toLowerCase() === '#000000') return '#ffffff';
    if (color.toLowerCase() === '#ffffff') return '#000000';
  }
  return color;
};

const getDisplayColor = (color: string, theme: string | undefined) => {
  if (!color) return '#000000'; // Return a default color if input is undefined
  if (color === 'auto') {
    return theme === 'dark' ? '#FFFFFF' : '#000000';
  }
  if (theme === 'dark') {
    if (color.toLowerCase() === '#000000') return '#ffffff';
    if (color.toLowerCase() === '#ffffff') return '#000000';
  }
  return color;
};

interface KineticCanvasProps {
  gravity: number;
  friction: number;
  restitution: number;
  textToRender: string | null;
  dropTrigger: number;
  isCharacterMode: boolean;
  isAltMode: boolean;
  onReset: number;
  isPaused: boolean;
  canvasColor: string;
  floorColor: string;
  textOptions: TextOptions;
  onDropComplete: () => void;
  deletePresetId: number | null;
  replaceTrigger: ReplaceTrigger | null;
  onReplaceComplete: () => void;
  modeSwapTrigger: ModeSwapTrigger | null;
  onModeSwapComplete: () => void;
  frozenPresetIds: Set<number>;
  wallOffsetX: number;
  wallOffsetY: number;
  wallBorderColor: string;
  drawingTool: 'pen' | 'eraser' | 'hand';
  penSize: number;
  penColor: string;
  isLowPoly: boolean;
  showInvisible: boolean;
  saveDrawingTrigger: number;
  drawingToLoad: any;
  setWallOffsetY: (value: React.SetStateAction<number>) => void;
}

// Custom hook to get window size
const useWindowSize = () => {
  const [size, setSize] = useState([0, 0]);
  useEffect(() => {
    function updateSize() {
      setSize([window.innerWidth, window.innerHeight]);
    }
    window.addEventListener('resize', updateSize);
    updateSize();
    return () => window.removeEventListener('resize', updateSize);
  }, []);
  return size;
};

const KineticCanvas: React.FC<KineticCanvasProps> = ({
  gravity, friction, restitution, textToRender, dropTrigger, isCharacterMode, isAltMode, onReset, isPaused, canvasColor, floorColor, textOptions, onDropComplete, deletePresetId, replaceTrigger, onReplaceComplete, modeSwapTrigger, onModeSwapComplete,
  frozenPresetIds,
  wallOffsetX,
  wallOffsetY,
  wallBorderColor,
  drawingTool,
  penSize,
  penColor,
  isLowPoly,
  showInvisible,
  saveDrawingTrigger,
  drawingToLoad,
  setWallOffsetY
}) => {
  const [width, height] = useWindowSize();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<Matter.Engine | null>(null);
  const renderRef = useRef<Matter.Render | null>(null);
  const runnerRef = useRef<Matter.Runner | null>(null);
  const measureCanvasRef = useRef<HTMLCanvasElement>(null);
  const prevWallOffsetYRef = useRef(wallOffsetY);
  const prevWallOffsetXRef = useRef(wallOffsetX);
  const mouseConstraintRef = useRef<Matter.MouseConstraint | null>(null);
  const isDrawing = useRef(false);
  const lastMousePosition = useRef<{ x: number; y: number } | null>(null);
  const mousePositionRef = useRef<{ x: number; y: number } | null>(null);
  const heldSurfaceInfoRef = useRef({ floor: false, leftWall: false, rightWall: false });
  const { theme } = useTheme();
  const themeRef = useRef(theme);

  useEffect(() => {
    themeRef.current = theme;
  }, [theme]);

  // Measure text dimensions
  const measureText = (text: string, options: TextOptions) => {
    const canvas = measureCanvasRef.current;
    if (!canvas) return { width: 0, height: 0 };

    const ctx = canvas.getContext("2d");
    if (!ctx) return { width: 0, height: 0 };

    const { fontSize, fontFamily, isBold, isItalic } = options;
    const fontStyle = isItalic ? 'italic' : 'normal';
    const fontWeight = isBold ? 'bold' : 'normal';
    ctx.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`;

    const lines = text.split('\n');
    const widths = lines.map(line => ctx.measureText(line).width);
    const maxWidth = Math.max(...widths);
    const lineHeight = fontSize * 1.2;
    const totalHeight = lines.length * lineHeight;

    return {
      width: maxWidth,
      height: totalHeight,
    };
  };

  // Create text body for full text mode
  const createTextBody = (text: string, x: number, y: number, options: TextOptions, isPreview = false) => {
    const { width, height } = measureText(text, options);
    const padding = 10;

    const bodyWidth = isAltMode && options.collisionBoxSizeX ? options.collisionBoxSizeX : width + padding * 2;
    const bodyHeight = isAltMode && options.collisionBoxSizeY ? options.collisionBoxSizeY : height + padding;

    const body = Matter.Bodies.rectangle(
      x,
      y,
      bodyWidth,
      bodyHeight,
      {
        friction,
        restitution,
        render: {
          fillStyle: isPreview ? 'transparent' : getResolvedBackgroundColor(options.collisionColor, themeRef.current),
          strokeStyle: isPreview ? getResolvedBackgroundColor(options.collisionColor, themeRef.current) : 'transparent',
          lineWidth: isPreview ? 2 : 0,
        },
      },
    );

    (body as BodyWithText).textData = { text, options: { ...options } }; // Use a copy to prevent state mutation
    if (isPreview) {
      (body as BodyWithText).isPreview = true;
      Matter.Body.setStatic(body, true);
    }
    return body;
  };

  // Create character bodies for character mode
  const createCharacterBodies = (text: string, startX: number, startY: number, options: TextOptions, isPreview = false) => {
    const bodies: Matter.Body[] = [];
    const lines = text.split('\n');
    const { fontSize, letterSpacing, collisionBoxSizeX, collisionBoxSizeY } = options;
    const lineHeight = fontSize * 1.2;

    // Find the max width of any character in the text for uniform sizing
    const allChars = text.replace(/\n/g, '').split('');
    const maxWidth = Math.max(...allChars.map(char => measureText(char, options).width));
    
    // Use collisionBoxSizeX for layout if provided, otherwise use measured width
    const layoutWidth = collisionBoxSizeX ?? maxWidth;

    const totalHeight = lines.length * lineHeight;
    const initialY = startY - totalHeight / 2;

    lines.forEach((line, lineIndex) => {
      const characters = line.split('');
      const totalWidth = (layoutWidth + letterSpacing) * characters.length;
      let currentX = startX - totalWidth / 2;
      const currentY = initialY + (lineIndex * lineHeight) + (lineHeight / 2);

      for (let i = 0; i < characters.length; i++) {
        const char = characters[i];
        
        if (char !== " ") {
          const padding = 2;
          // Use collisionBoxSize for body dimensions if it exists, otherwise measure text with padding
          const bodyWidth = collisionBoxSizeX ?? maxWidth + padding * 2;
          const bodyHeight = collisionBoxSizeY ?? measureText(char, options).height + padding;

          const charOptions = { ...options };
          const body = Matter.Bodies.rectangle(
            currentX + layoutWidth / 2,
            currentY,
            bodyWidth,
            bodyHeight,
            {
              friction,
              restitution,
              render: {
                fillStyle: isPreview ? 'transparent' : getResolvedBackgroundColor(options.collisionColor, themeRef.current),
                strokeStyle: isPreview ? getResolvedBackgroundColor(options.collisionColor, themeRef.current) : 'transparent',
                lineWidth: isPreview ? 2 : 0,
              },
            },
          );

          (body as BodyWithText).textData = { text: char, options: charOptions };
          if (isPreview) {
            (body as BodyWithText).isPreview = true;
            Matter.Body.setStatic(body, true);
          }
          bodies.push(body);
        }
        // Always advance by the same amount to maintain spacing
        currentX += layoutWidth + letterSpacing;
      }
    });

    return bodies;
  };

  // Drawing logic
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !engineRef.current) return;

    const engine = engineRef.current;
    const world = engine.world;

    const handleMouseDown = (e: MouseEvent) => {
      isDrawing.current = true;
      lastMousePosition.current = { x: e.offsetX, y: e.offsetY };
    };

    const handleMouseMove = (e: MouseEvent) => {
      const currentPosition = { x: e.offsetX, y: e.offsetY };
      mousePositionRef.current = currentPosition;

      if (!isDrawing.current) return;

      if (drawingTool === 'pen') {
        if (lastMousePosition.current) {
          const distance = Matter.Vector.magnitude(Matter.Vector.sub(currentPosition, lastMousePosition.current));

          // Adjust distance threshold for low-poly mode
          const distanceThreshold = isLowPoly ? penSize * 2 : penSize / 4;
          
          // Only draw if the distance is greater than a threshold to prevent too many bodies
          if (distance > distanceThreshold) {
            const angle = Matter.Vector.angle(currentPosition, lastMousePosition.current);
            const isInvisible = penColor === 'transparent';

            const body = Matter.Bodies.rectangle(
              (currentPosition.x + lastMousePosition.current.x) / 2,
              (currentPosition.y + lastMousePosition.current.y) / 2,
              distance,
              penSize,
              {
                isStatic: true,
                angle: angle,
                render: {
                  fillStyle: isInvisible
                    ? (showInvisible ? 'rgba(128, 128, 128, 0.5)' : 'transparent')
                    : getDisplayColor(penColor, themeRef.current),
                },
                label: 'drawnLine',
              }
            );

            if (isInvisible) {
              (body as any).isInvisible = true;
            }
            (body as any).originalColor = penColor;

            Matter.Composite.add(world, body);
            lastMousePosition.current = currentPosition;
          }
        }
      } else if (drawingTool === 'eraser') {
        const eraserRadius = penSize;
        const region = {
            min: { x: currentPosition.x - eraserRadius, y: currentPosition.y - eraserRadius },
            max: { x: currentPosition.x + eraserRadius, y: currentPosition.y + eraserRadius }
        };
        const bodiesToRemove = Matter.Query.region(Matter.Composite.allBodies(world), region);
        bodiesToRemove.forEach(body => {
          if (body.label === 'drawnLine') {
            Matter.Composite.remove(world, body);
          }
        });
      }
    };

    const handleMouseUp = () => {
      isDrawing.current = false;
      lastMousePosition.current = null;
    };

    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);
    canvas.addEventListener('mouseleave', handleMouseUp);

    return () => {
      canvas.removeEventListener('mousedown', handleMouseDown);
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('mouseup', handleMouseUp);
      canvas.removeEventListener('mouseleave', handleMouseUp);
    };
    }, [drawingTool, penSize, penColor, isLowPoly, showInvisible]);

  // Update drawing colors on theme change
  useEffect(() => {
    const world = engineRef.current?.world;
    if (!world) return;

    const allBodies = Matter.Composite.allBodies(world);
    allBodies.forEach(body => {
      if (body.label === 'drawnLine') {
        const originalColor = (body as any).originalColor;
        const isInvisible = (body as any).isInvisible;

        if (isInvisible) {
          body.render.fillStyle = showInvisible ? 'rgba(128, 128, 128, 0.5)' : 'transparent';
        } else if (originalColor) {
          body.render.fillStyle = getDisplayColor(originalColor, theme);
        }
      }
    });
  }, [theme, showInvisible]);

  // Initialize physics engine
  useEffect(() => {
    if (!canvasRef.current || !width || !height) return;

    const engine = Matter.Engine.create({
      positionIterations: 10,
      velocityIterations: 8,
    });
    const world = engine.world;

    const render = Matter.Render.create({
      canvas: canvasRef.current,
      engine: engine,
      options: {
        width: width,
        height: height,
        wireframes: false,
        background: canvasColor,
        showAngleIndicator: false,
        showVelocity: false,
      },
    });

    const handleWheel = (event: WheelEvent) => {
      event.preventDefault();
      setWallOffsetY(prevOffsetY => prevOffsetY + event.deltaY);
    };

    const canvas = render.canvas;
    canvas.addEventListener('wheel', handleWheel);

    const ground = Matter.Bodies.rectangle(width / 2, height - 10, width, 20, {
      label: 'ground',
      isStatic: true,
      render: {
        fillStyle: floorColor,
        strokeStyle: wallBorderColor,
        lineWidth: 2,
      },
    });
    const leftWall = Matter.Bodies.rectangle(-10, height / 2, 20, height, {
      label: 'leftWall',
      isStatic: true,
      render: {
        fillStyle: floorColor,
        strokeStyle: wallBorderColor,
        lineWidth: 2,
      },
    });
    const rightWall = Matter.Bodies.rectangle(width + 10, height / 2, 20, height, {
      label: 'rightWall',
      isStatic: true,
      render: {
        fillStyle: floorColor,
        strokeStyle: wallBorderColor,
        lineWidth: 2,
      },
    });

    const mouse = Matter.Mouse.create(render.canvas);
    const mouseConstraint = Matter.MouseConstraint.create(engine, {
      mouse: mouse,
      constraint: {
        stiffness: 0.2,
        render: {
          visible: false,
        },
      },
    });
    mouseConstraintRef.current = mouseConstraint;

    // On release, if the object was held against a surface, cancel its velocity to prevent it from flying away.
    Matter.Events.on(mouseConstraint, 'enddrag', (event) => {
      const body = (event as any).body;
      const { floor, leftWall, rightWall } = heldSurfaceInfoRef.current;

      if (floor || leftWall || rightWall) {
        const newVelocity = { ...body.velocity };
        if (floor) {
          newVelocity.y = 0;
        }
        if (leftWall || rightWall) {
          newVelocity.x = 0;
        }
        Matter.Body.setVelocity(body, newVelocity);
      }
      
      // Reset after drag ends
      heldSurfaceInfoRef.current = { floor: false, leftWall: false, rightWall: false };
    });

    // Prevent objects from visually passing through walls/floor during a drag.
    Matter.Events.on(engine, 'afterUpdate', () => {
      if (mouseConstraint.body) {
        const body = mouseConstraint.body;
        const ground = Matter.Composite.allBodies(world).find(b => b.label === 'ground');
        const leftWall = Matter.Composite.allBodies(world).find(b => b.label === 'leftWall');
        const rightWall = Matter.Composite.allBodies(world).find(b => b.label === 'rightWall');

        const heldInfo = { floor: false, leftWall: false, rightWall: false };

        // Floor collision
        if (ground) {
          const groundTop = ground.position.y - 10;
          const lowestPointY = Math.max(...body.vertices.map(v => v.y));
          if (lowestPointY > groundTop) {
            const penetration = lowestPointY - groundTop;
            Matter.Body.translate(body, { x: 0, y: -penetration });
            heldInfo.floor = true;
          }
        }

        // Left wall collision
        if (leftWall) {
          const wallRightEdge = leftWall.position.x + 10;
          const leftmostPointX = Math.min(...body.vertices.map(v => v.x));
          if (leftmostPointX < wallRightEdge) {
            const penetration = wallRightEdge - leftmostPointX;
            Matter.Body.translate(body, { x: penetration, y: 0 });
            heldInfo.leftWall = true;
          }
        }

        // Right wall collision
        if (rightWall) {
          const wallLeftEdge = rightWall.position.x - 10;
          const rightmostPointX = Math.max(...body.vertices.map(v => v.x));
          if (rightmostPointX > wallLeftEdge) {
            const penetration = rightmostPointX - wallLeftEdge;
            Matter.Body.translate(body, { x: -penetration, y: 0 });
            heldInfo.rightWall = true;
          }
        }
        
        heldSurfaceInfoRef.current = heldInfo;

      } else {
        // Reset if not dragging
        heldSurfaceInfoRef.current = { floor: false, leftWall: false, rightWall: false };
      }
    });

    Matter.Composite.add(world, [ground, leftWall, rightWall, mouseConstraint]);

    // Use the 'afterRender' event to draw text on top of the bodies
    Matter.Events.on(render, 'afterRender', () => {
      const bodies = Matter.Composite.allBodies(world);
      const ctx = render.context;

      bodies.forEach((body) => {
        const bodyWithText = body as BodyWithText;
        if (bodyWithText.textData) {
          ctx.save();
          ctx.translate(body.position.x, body.position.y);
          ctx.rotate(body.angle);
          const { text, options } = bodyWithText.textData;
          const { fontSize, fontFamily, isBold, isItalic, color } = options;
          const fontStyle = isItalic ? 'italic' : 'normal';
          const fontWeight = isBold ? 'bold' : 'normal';

          const currentTheme = themeRef.current;
          const finalColor = color === 'auto' 
            ? (currentTheme === 'dark' ? '#ffffff' : '#000000') 
            : getDisplayColor(color, currentTheme);
          ctx.fillStyle = finalColor;
          
          ctx.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`;
          ctx.textAlign = "center";
          ctx.textBaseline = "middle";

          const lines = text.split('\n');
          const lineHeight = fontSize * 1.2;
          const totalHeight = lines.length * lineHeight;
          const startY = -totalHeight / 2 + lineHeight / 2;

          lines.forEach((line, index) => {
            const yPos = startY + (index * lineHeight);
            ctx.fillText(line, 0, yPos);
          });

          ctx.restore();
        }
      });

      // Draw tool cursor
      if ((drawingTool === 'pen' || drawingTool === 'eraser') && mousePositionRef.current) {
        const mousePos = mousePositionRef.current;
        const radius = drawingTool === 'pen' ? penSize / 2 : penSize;
        
        ctx.beginPath();
        ctx.arc(mousePos.x, mousePos.y, radius, 0, 2 * Math.PI);

        if (drawingTool === 'pen') {
          ctx.fillStyle = getDisplayColor(penColor, theme);
          ctx.globalAlpha = 0.5;
          ctx.fill();
          ctx.globalAlpha = 1.0; // Reset alpha
        } else { // eraser
          ctx.fillStyle = 'rgba(255, 0, 0, 0.2)';
          ctx.fill();
          ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';
          ctx.lineWidth = 1;
          ctx.stroke();
        }
      }
    });

    const runner = Matter.Runner.create();
    Matter.Runner.run(runner, engine);
    Matter.Render.run(render);

    engineRef.current = engine;
    renderRef.current = render;
    runnerRef.current = runner;

    return () => {
      canvas.removeEventListener('wheel', handleWheel);
      if (renderRef.current) Matter.Render.stop(renderRef.current);
      if (runnerRef.current) Matter.Runner.stop(runnerRef.current);
      if (engineRef.current) Matter.Engine.clear(engineRef.current);
    };
  }, [width, height]); // Re-initialize on resize

  // Save drawing to JSON
  useEffect(() => {
    if (saveDrawingTrigger === 0) return;

    const world = engineRef.current?.world;
    if (!world) return;

    const drawnLines = Matter.Composite.allBodies(world).filter(
      (body) => body.label === 'drawnLine'
    );

    const drawingData = drawnLines.map((body) => {
      const width = Matter.Vector.magnitude(Matter.Vector.sub(body.vertices[1], body.vertices[0]));
      const height = Matter.Vector.magnitude(Matter.Vector.sub(body.vertices[2], body.vertices[1]));
      return {
        position: body.position,
        angle: body.angle,
        width: width,
        height: height,
        render: {
          fillStyle: body.render.fillStyle,
        },
        isInvisible: (body as any).isInvisible || false,
        originalColor: (body as any).originalColor || body.render.fillStyle,
      };
    });

    const json = JSON.stringify({ drawing: drawingData }, null, 2);
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'drawing.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

  }, [saveDrawingTrigger]);

  // Load drawing from JSON
  useEffect(() => {
    if (!drawingToLoad) return;

    const world = engineRef.current?.world;
    if (!world) return;

    // Clear existing drawing
    const existingLines = Matter.Composite.allBodies(world).filter(
      (body) => body.label === 'drawnLine'
    );
    Matter.Composite.remove(world, existingLines);

    // Add new lines
    drawingToLoad.drawing.forEach((lineData: any) => {
      const body = Matter.Bodies.rectangle(
        lineData.position.x,
        lineData.position.y,
        lineData.width,
        lineData.height,
        {
          isStatic: true,
          angle: lineData.angle,
          render: {
            fillStyle: lineData.isInvisible
              ? (showInvisible ? 'rgba(128, 128, 128, 0.5)' : 'transparent')
              : getDisplayColor(lineData.originalColor, theme),
          },
          label: 'drawnLine',
        }
      );

      if (lineData.isInvisible) {
        (body as any).isInvisible = true;
      }
      (body as any).originalColor = lineData.originalColor;

      Matter.Composite.add(world, body);
    });

  }, [drawingToLoad]);

  // Update drawings on theme change
  useEffect(() => {
    if (!engineRef.current) return;
    const world = engineRef.current.world;
    const allBodies = Matter.Composite.allBodies(world);

    allBodies.forEach(body => {
      if (body.label === 'drawnLine' && (body as any).originalColor && !(body as any).isInvisible) {
        body.render.fillStyle = getDisplayColor((body as any).originalColor, theme);
      }
    });
  }, [theme]);

  // Toggle visibility of invisible lines
  useEffect(() => {
    if (!engineRef.current) return;
    const world = engineRef.current.world;
    const allBodies = Matter.Composite.allBodies(world);

    allBodies.forEach(body => {
      if ((body as any).isInvisible) {
        body.render.fillStyle = showInvisible ? 'rgba(128, 128, 128, 0.5)' : 'transparent';
      }
    });
  }, [showInvisible]);

  // Update physics parameters
  useEffect(() => {
    if (engineRef.current) {
      engineRef.current.world.gravity.y = gravity;
      const bodies = Matter.Composite.allBodies(engineRef.current.world);
      bodies.forEach((body) => {
        if (!body.isStatic) {
          body.friction = friction;
          body.restitution = restitution;
        }
      });
    }
  }, [gravity, friction, restitution]);

  // Pause/Play simulation
  useEffect(() => {
    if (engineRef.current) {
      engineRef.current.timing.timeScale = isPaused ? 0 : 1;
    }
  }, [isPaused]);

  // Disable mouse constraint when drawing
  useEffect(() => {
    if (mouseConstraintRef.current) {
      const isDrawingActive = drawingTool === 'pen' || drawingTool === 'eraser';
      if (mouseConstraintRef.current.mouse.element) {
        // mouseConstraintRef.current.mouse.element.style.cursor = isDrawingActive ? 'none' : 'auto';
      }
      mouseConstraintRef.current.constraint.stiffness = isDrawingActive ? 0 : 0.2;
    }
  }, [drawingTool]);

  // Sync all dynamic bodies with wall offsets
  useEffect(() => {
    if (!engineRef.current) return;

    const deltaY = wallOffsetY - prevWallOffsetYRef.current;
    const deltaX = wallOffsetX - prevWallOffsetXRef.current;

    if (deltaY === 0 && deltaX === 0) return;

    const world = engineRef.current.world;
    const allBodies = Matter.Composite.allBodies(world);
    const bodiesToMove = allBodies.filter(
      b => b.label !== 'ground' && b.label !== 'leftWall' && b.label !== 'rightWall'
    );

    bodiesToMove.forEach(body => {
      Matter.Body.translate(body, { x: deltaX, y: deltaY });
    });

    prevWallOffsetYRef.current = wallOffsetY;
    prevWallOffsetXRef.current = wallOffsetX;
  }, [wallOffsetY, wallOffsetX]);

  // Update wall/floor positions and borders for debugging
  useEffect(() => {
    if (!engineRef.current || !width || !height) return;

    const world = engineRef.current.world;
    const ground = Matter.Composite.allBodies(world).find(b => b.label === 'ground');
    const leftWall = Matter.Composite.allBodies(world).find(b => b.label === 'leftWall');
    const rightWall = Matter.Composite.allBodies(world).find(b => b.label === 'rightWall');

    if (ground) {
      Matter.Body.setPosition(ground, { x: width / 2 + wallOffsetX, y: height - 10 + wallOffsetY });
      ground.render.strokeStyle = wallBorderColor;
    }
    if (leftWall) {
      Matter.Body.setPosition(leftWall, { x: -10 + wallOffsetX, y: height / 2 + wallOffsetY });
      leftWall.render.strokeStyle = wallBorderColor;
    }
    if (rightWall) {
      Matter.Body.setPosition(rightWall, { x: width + 10 + wallOffsetX, y: height / 2 + wallOffsetY });
      rightWall.render.strokeStyle = wallBorderColor;
    }
  }, [wallOffsetX, wallOffsetY, wallBorderColor, width, height]);

  // Update canvas color
  useEffect(() => {
    if (renderRef.current && engineRef.current) {
      renderRef.current.options.background = canvasColor;
      const world = engineRef.current.world;
      const ground = Matter.Composite.allBodies(world).find(b => b.label === 'ground');
      const leftWall = Matter.Composite.allBodies(world).find(b => b.label === 'leftWall');
      const rightWall = Matter.Composite.allBodies(world).find(b => b.label === 'rightWall');
      if(ground) ground.render.fillStyle = floorColor;
      if(leftWall) leftWall.render.fillStyle = floorColor;
      if(rightWall) rightWall.render.fillStyle = floorColor;
    }
  }, [canvasColor, floorColor]);

  // Render preview text
  useEffect(() => {
    if (!engineRef.current || !width || !height) return;
    const world = engineRef.current.world;

    const oldPreviewBodies = Matter.Composite.allBodies(world).filter(b => (b as BodyWithText).isPreview);
    if (oldPreviewBodies.length > 0) {
      Matter.Composite.remove(world, oldPreviewBodies);
    }

    if (textToRender) {
      const x = width / 2;
      const y = height / 4; // Position preview higher up
      const isPreview = true;

      if (isCharacterMode) {
        const bodies = createCharacterBodies(textToRender, x, y, textOptions, isPreview);
        Matter.Composite.add(world, bodies);
      } else {
        const body = createTextBody(textToRender, x, y, textOptions, isPreview);
        Matter.Composite.add(world, body);
      }
    }
  }, [textToRender, isCharacterMode, isAltMode, textOptions, width, height]);

  // Drop the rendered text
  useEffect(() => {
    if (dropTrigger === 0 || !engineRef.current) return;

    const world = engineRef.current.world;
    const previewBodies = Matter.Composite.allBodies(world).filter(b => (b as BodyWithText).isPreview);

    if (previewBodies.length > 0) {
      previewBodies.forEach(body => {
        const bodyWithText = body as BodyWithText;
        if (bodyWithText.textData) {
          body.render.fillStyle = getResolvedBackgroundColor(bodyWithText.textData.options.collisionColor, themeRef.current);
          body.render.strokeStyle = 'transparent';
          body.render.lineWidth = 0;
        }
        Matter.Body.setStatic(body, false);
        bodyWithText.isPreview = false;
        bodyWithText.presetId = dropTrigger; // Assign the preset ID
      });
      onDropComplete();
    }
  }, [dropTrigger, onDropComplete]);

  // Reset physics world
  useEffect(() => {
    if (!onReset || !engineRef.current) return;
    const world = engineRef.current.world;
    const bodies = Matter.Composite.allBodies(world);
    const dynamicBodies = bodies.filter((body) => !body.isStatic);
    Matter.Composite.remove(world, dynamicBodies);
  }, [onReset]);

  // Delete preset bodies
  useEffect(() => {
    if (deletePresetId === null || !engineRef.current) return;

    const world = engineRef.current.world;
    const bodiesToDelete = Matter.Composite.allBodies(world).filter(
      (body) => (body as BodyWithText).presetId === deletePresetId
    );

    if (bodiesToDelete.length > 0) {
      Matter.Composite.remove(world, bodiesToDelete);
    }
  }, [deletePresetId]);

  // Replace preset text
  // Freeze/unfreeze preset bodies
  useEffect(() => {
    if (!engineRef.current) return;

    const world = engineRef.current.world;
    const allBodies = Matter.Composite.allBodies(world);

    allBodies.forEach(body => {
      const bodyWithText = body as BodyWithText;
      if (bodyWithText.presetId !== undefined) {
        const shouldBeFrozen = frozenPresetIds.has(bodyWithText.presetId);
        if (body.isStatic !== shouldBeFrozen) {
          Matter.Body.setStatic(body, shouldBeFrozen);
        }
      }
    });
  }, [frozenPresetIds]);

  // Replace preset text
  useEffect(() => {
    if (!replaceTrigger || !engineRef.current) return;

    const { presetId, altFilename } = replaceTrigger;
    const world = engineRef.current.world;

    const bodiesToReplace = Matter.Composite.allBodies(world).filter(
      (body) => (body as BodyWithText).presetId === presetId
    );

    if (bodiesToReplace.length === 0) {
      onReplaceComplete();
      return;
    }

    // Calculate the center of the existing bodies
    const centerX = bodiesToReplace.reduce((sum, body) => sum + body.position.x, 0) / bodiesToReplace.length;
    const centerY = bodiesToReplace.reduce((sum, body) => sum + body.position.y, 0) / bodiesToReplace.length;

    const fetchAndReplace = async () => {
      try {
        const response = await fetch(`/presets/${altFilename}`);
        if (!response.ok) throw new Error(`Failed to fetch preset file: ${altFilename}`);
        const newText = await response.text();

        // Remove old bodies
        Matter.Composite.remove(world, bodiesToReplace);

        // Create and add new bodies
        let newBodies: Matter.Body[];
        if (isCharacterMode) {
          newBodies = createCharacterBodies(newText, centerX, centerY, textOptions);
        } else {
          newBodies = [createTextBody(newText, centerX, centerY, textOptions)];
        }

        newBodies.forEach(body => {
          (body as BodyWithText).presetId = presetId; // Re-assign the same preset ID
        });

        Matter.Composite.add(world, newBodies);

      } catch (error) {
        console.error("Failed to replace text:", error);
      } finally {
        onReplaceComplete(); // Reset the trigger
      }
    };

    fetchAndReplace();
  }, [replaceTrigger, isCharacterMode, textOptions, onReplaceComplete]);

  // Mode swap effect
  useEffect(() => {
    if (!modeSwapTrigger || !engineRef.current) return;

    const { presetId, newMode } = modeSwapTrigger;
    const world = engineRef.current.world;

    const bodiesToSwap = Matter.Composite.allBodies(world).filter(
      (body) => (body as BodyWithText).presetId === presetId
    );

    if (bodiesToSwap.length === 0) {
      onModeSwapComplete();
      return;
    }

    // Calculate the center of the existing bodies
    const centerX = bodiesToSwap.reduce((sum, body) => sum + body.position.x, 0) / bodiesToSwap.length;
    const centerY = bodiesToSwap.reduce((sum, body) => sum + body.position.y, 0) / bodiesToSwap.length;

    // Get the text from the first body (they should all have the same text)
    const firstBody = bodiesToSwap[0] as BodyWithText;
    if (!firstBody.textData) {
      onModeSwapComplete();
      return;
    }

    const { text, options } = firstBody.textData;

    // Remove old bodies
    Matter.Composite.remove(world, bodiesToSwap);

    // Create new bodies with the new mode
    let newBodies: Matter.Body[];
    if (newMode === 'character') {
      newBodies = createCharacterBodies(text, centerX, centerY, options);
    } else {
      newBodies = [createTextBody(text, centerX, centerY, options)];
    }

    // Re-assign the same preset ID and preserve frozen state
    newBodies.forEach(body => {
      (body as BodyWithText).presetId = presetId;
      // Check if this preset was frozen and apply the same state
      if (frozenPresetIds.has(presetId)) {
        Matter.Body.setStatic(body, true);
      }
    });

    Matter.Composite.add(world, newBodies);
    onModeSwapComplete();
  }, [modeSwapTrigger, createCharacterBodies, createTextBody, frozenPresetIds, onModeSwapComplete]);

  return (
    <div className="w-full h-full">
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
      />
      <canvas
        ref={measureCanvasRef}
        width={1}
        height={1}
        className="hidden"
      />
    </div>
  );
};

export default KineticCanvas;
