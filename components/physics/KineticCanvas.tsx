import { useTheme } from 'next-themes';
import { useEffect, useRef, useState } from 'react';
import Matter from 'matter-js';
import { TextOptions, BodyWithText } from './types';
import PerformanceMonitor from './PerformanceMonitor';

interface ReplaceTrigger {
  presetId: number;
  altFilename: string;
}

interface ModeSwapTrigger {
  presetId: number;
  newMode: 'default' | 'character';
}

const getResolvedBackgroundColor = (color: string, theme: string | undefined) => {
  if (color === 'auto') {
    return 'transparent';
  }
  if (theme === 'dark') {
    if (color.toLowerCase() === '#000000') return '#ffffff';
    if (color.toLowerCase() === '#ffffff') return '#000000';
  }
  return color;
};

const getDisplayColor = (color: string, theme: string | undefined) => {
  if (!color) return '#000000'; // Return a default color if input is undefined
  if (color === 'auto') {
    return theme === 'dark' ? '#FFFFFF' : '#000000';
  }
  if (theme === 'dark') {
    if (color.toLowerCase() === '#000000') return '#ffffff';
    if (color.toLowerCase() === '#ffffff') return '#000000';
  }
  return color;
};

interface KineticCanvasProps {
  gravity: number;
  friction: number;
  restitution: number;
  textToRender: string | null;
  dropTrigger: number;
  isCharacterMode: boolean;
  isAltMode: boolean;
  onReset: number;
  isPaused: boolean;
  canvasColor: string;
  floorColor: string;
  textOptions: TextOptions;
  onDropComplete: () => void;
  deletePresetId: number | null;
  replaceTrigger: ReplaceTrigger | null;
  onReplaceComplete: () => void;
  modeSwapTrigger: ModeSwapTrigger | null;
  onModeSwapComplete: () => void;
  frozenPresetIds: Set<number>;
  wallOffsetX: number;
  wallOffsetY: number;
  wallBorderColor: string;
  drawingTool: 'pen' | 'eraser' | 'hand';
  penSize: number;
  penColor: string;
  isLowPoly: boolean;
  showInvisible: boolean;
  saveDrawingTrigger: number;
  drawingToLoad: any;
  setWallOffsetY: (value: React.SetStateAction<number>) => void;
  showPerformanceMonitor?: boolean;
  performanceOptimizations?: {
    suspendStaticBodies: boolean;
    disableCollisionDetection: boolean;
    reduceRenderFrequency: boolean;
    freezeDistantBodies: boolean;
    batchPhysicsUpdates: boolean;
  };
}

// Custom hook to get window size
const useWindowSize = () => {
  const [size, setSize] = useState([0, 0]);
  useEffect(() => {
    function updateSize() {
      setSize([window.innerWidth, window.innerHeight]);
    }
    window.addEventListener('resize', updateSize);
    updateSize();
    return () => window.removeEventListener('resize', updateSize);
  }, []);
  return size;
};

const KineticCanvas: React.FC<KineticCanvasProps> = ({
  gravity, friction, restitution, textToRender, dropTrigger, isCharacterMode, isAltMode, onReset, isPaused, canvasColor, floorColor, textOptions, onDropComplete, deletePresetId, replaceTrigger, onReplaceComplete, modeSwapTrigger, onModeSwapComplete,
  frozenPresetIds,
  wallOffsetX,
  wallOffsetY,
  wallBorderColor,
  drawingTool,
  penSize,
  penColor,
  isLowPoly,
  showInvisible,
  saveDrawingTrigger,
  drawingToLoad,
  setWallOffsetY,
  showPerformanceMonitor = false,
  performanceOptimizations = {
    suspendStaticBodies: false,
    disableCollisionDetection: false,
    reduceRenderFrequency: false,
    freezeDistantBodies: false,
    batchPhysicsUpdates: false,
  }
}) => {
  const [width, height] = useWindowSize();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<Matter.Engine | null>(null);
  const renderRef = useRef<Matter.Render | null>(null);
  const runnerRef = useRef<Matter.Runner | null>(null);
  const measureCanvasRef = useRef<HTMLCanvasElement>(null);
  const prevWallOffsetYRef = useRef(wallOffsetY);
  const prevWallOffsetXRef = useRef(wallOffsetX);
  const mouseConstraintRef = useRef<Matter.MouseConstraint | null>(null);
  const isDrawing = useRef(false);
  const lastMousePosition = useRef<{ x: number; y: number } | null>(null);
  const mousePositionRef = useRef<{ x: number; y: number } | null>(null);
  const heldSurfaceInfoRef = useRef({ floor: false, leftWall: false, rightWall: false });
  const { theme } = useTheme();
  const themeRef = useRef(theme);

  // Performance optimization: Cache font strings and frame tracking
  const fontCache = useRef<Map<string, string>>(new Map());
  const frameCounter = useRef<number>(0);
  const lastBodyMovementCheck = useRef<Map<number, { x: number; y: number; time: number }>>(new Map());

  useEffect(() => {
    themeRef.current = theme;
  }, [theme]);

  // Measure text dimensions
  const measureText = (text: string, options: TextOptions) => {
    const canvas = measureCanvasRef.current;
    if (!canvas) return { width: 0, height: 0 };

    const ctx = canvas.getContext("2d");
    if (!ctx) return { width: 0, height: 0 };

    const { fontSize, fontFamily, isBold, isItalic } = options;
    const fontStyle = isItalic ? 'italic' : 'normal';
    const fontWeight = isBold ? 'bold' : 'normal';
    ctx.font = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`;

    const lines = text.split('\n');
    const widths = lines.map(line => ctx.measureText(line).width);
    const maxWidth = Math.max(...widths);
    const lineHeight = fontSize * 1.2;
    const totalHeight = lines.length * lineHeight;

    return {
      width: maxWidth,
      height: totalHeight,
    };
  };

  // Create text body for full text mode
  const createTextBody = (text: string, x: number, y: number, options: TextOptions, isPreview = false) => {
    const { width, height } = measureText(text, options);
    const padding = 10;

    const bodyWidth = isAltMode && options.collisionBoxSizeX ? options.collisionBoxSizeX : width + padding * 2;
    const bodyHeight = isAltMode && options.collisionBoxSizeY ? options.collisionBoxSizeY : height + padding;

    const body = Matter.Bodies.rectangle(
      x,
      y,
      bodyWidth,
      bodyHeight,
      {
        friction,
        restitution,
        render: {
          fillStyle: isPreview ? 'transparent' : getResolvedBackgroundColor(options.collisionColor, themeRef.current),
          strokeStyle: isPreview ? getResolvedBackgroundColor(options.collisionColor, themeRef.current) : 'transparent',
          lineWidth: isPreview ? 2 : 0,
        },
        // Performance optimization: Collision filtering
        collisionFilter: {
          category: performanceOptimizations.disableCollisionDetection ? 0x0002 : 0x0001,
          mask: performanceOptimizations.disableCollisionDetection ? 0x0004 : 0xFFFFFFFF, // Only collide with walls/floor when optimized
        },
      },
    );

    (body as BodyWithText).textData = { text, options: { ...options } }; // Use a copy to prevent state mutation
    if (isPreview) {
      (body as BodyWithText).isPreview = true;
      Matter.Body.setStatic(body, true);
    }
    return body;
  };

  // Create character bodies for character mode
  const createCharacterBodies = (text: string, startX: number, startY: number, options: TextOptions, isPreview = false) => {
    const bodies: Matter.Body[] = [];
    const lines = text.split('\n');
    const { fontSize, letterSpacing, collisionBoxSizeX, collisionBoxSizeY } = options;
    const lineHeight = fontSize * 1.2;

    // Find the max width of any character in the text for uniform sizing
    const allChars = text.replace(/\n/g, '').split('');
    const maxWidth = Math.max(...allChars.map(char => measureText(char, options).width));
    
    // Use collisionBoxSizeX for layout if provided, otherwise use measured width
    const layoutWidth = collisionBoxSizeX ?? maxWidth;

    const totalHeight = lines.length * lineHeight;
    const initialY = startY - totalHeight / 2;

    lines.forEach((line, lineIndex) => {
      const characters = line.split('');
      const totalWidth = (layoutWidth + letterSpacing) * characters.length;
      let currentX = startX - totalWidth / 2;
      const currentY = initialY + (lineIndex * lineHeight) + (lineHeight / 2);

      for (let i = 0; i < characters.length; i++) {
        const char = characters[i];
        
        if (char !== " ") {
          const padding = 2;
          // Use collisionBoxSize for body dimensions if it exists, otherwise measure text with padding
          const bodyWidth = collisionBoxSizeX ?? maxWidth + padding * 2;
          const bodyHeight = collisionBoxSizeY ?? measureText(char, options).height + padding;

          const charOptions = { ...options };
          const body = Matter.Bodies.rectangle(
            currentX + layoutWidth / 2,
            currentY,
            bodyWidth,
            bodyHeight,
            {
              friction,
              restitution,
              render: {
                fillStyle: isPreview ? 'transparent' : getResolvedBackgroundColor(options.collisionColor, themeRef.current),
                strokeStyle: isPreview ? getResolvedBackgroundColor(options.collisionColor, themeRef.current) : 'transparent',
                lineWidth: isPreview ? 2 : 0,
              },
              // Performance optimization: Collision filtering
              collisionFilter: {
                category: performanceOptimizations.disableCollisionDetection ? 0x0002 : 0x0001,
                mask: performanceOptimizations.disableCollisionDetection ? 0x0004 : 0xFFFFFFFF, // Only collide with walls/floor when optimized
              },
            },
          );

          (body as BodyWithText).textData = { text: char, options: charOptions };
          if (isPreview) {
            (body as BodyWithText).isPreview = true;
            Matter.Body.setStatic(body, true);
          }
          bodies.push(body);
        }
        // Always advance by the same amount to maintain spacing
        currentX += layoutWidth + letterSpacing;
      }
    });

    return bodies;
  };

  // Drawing logic
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !engineRef.current) return;

    const engine = engineRef.current;
    const world = engine.world;

    const handleMouseDown = (e: MouseEvent) => {
      isDrawing.current = true;
      lastMousePosition.current = { x: e.offsetX, y: e.offsetY };
    };

    const handleMouseMove = (e: MouseEvent) => {
      const currentPosition = { x: e.offsetX, y: e.offsetY };
      mousePositionRef.current = currentPosition;

      if (!isDrawing.current) return;

      if (drawingTool === 'pen') {
        if (lastMousePosition.current) {
          const distance = Matter.Vector.magnitude(Matter.Vector.sub(currentPosition, lastMousePosition.current));

          // Adjust distance threshold for low-poly mode
          const distanceThreshold = isLowPoly ? penSize * 2 : penSize / 4;
          
          // Only draw if the distance is greater than a threshold to prevent too many bodies
          if (distance > distanceThreshold) {
            const angle = Matter.Vector.angle(currentPosition, lastMousePosition.current);
            const isInvisible = penColor === 'transparent';

            const body = Matter.Bodies.rectangle(
              (currentPosition.x + lastMousePosition.current.x) / 2,
              (currentPosition.y + lastMousePosition.current.y) / 2,
              distance,
              penSize,
              {
                isStatic: true,
                angle: angle,
                render: {
                  fillStyle: isInvisible
                    ? (showInvisible ? 'rgba(128, 128, 128, 0.5)' : 'transparent')
                    : getDisplayColor(penColor, themeRef.current),
                },
                label: 'drawnLine',
              }
            );

            if (isInvisible) {
              (body as any).isInvisible = true;
            }
            (body as any).originalColor = penColor;

            Matter.Composite.add(world, body);
            lastMousePosition.current = currentPosition;
          }
        }
      } else if (drawingTool === 'eraser') {
        const eraserRadius = penSize;
        const region = {
            min: { x: currentPosition.x - eraserRadius, y: currentPosition.y - eraserRadius },
            max: { x: currentPosition.x + eraserRadius, y: currentPosition.y + eraserRadius }
        };
        const bodiesToRemove = Matter.Query.region(Matter.Composite.allBodies(world), region);
        bodiesToRemove.forEach(body => {
          if (body.label === 'drawnLine') {
            Matter.Composite.remove(world, body);
          }
        });
      }
    };

    const handleMouseUp = () => {
      isDrawing.current = false;
      lastMousePosition.current = null;
    };

    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);
    canvas.addEventListener('mouseleave', handleMouseUp);

    return () => {
      canvas.removeEventListener('mousedown', handleMouseDown);
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('mouseup', handleMouseUp);
      canvas.removeEventListener('mouseleave', handleMouseUp);
    };
    }, [drawingTool, penSize, penColor, isLowPoly, showInvisible]);

  // Update drawing colors on theme change
  useEffect(() => {
    const world = engineRef.current?.world;
    if (!world) return;

    const allBodies = Matter.Composite.allBodies(world);
    allBodies.forEach(body => {
      if (body.label === 'drawnLine') {
        const originalColor = (body as any).originalColor;
        const isInvisible = (body as any).isInvisible;

        if (isInvisible) {
          body.render.fillStyle = showInvisible ? 'rgba(128, 128, 128, 0.5)' : 'transparent';
        } else if (originalColor) {
          body.render.fillStyle = getDisplayColor(originalColor, theme);
        }
      }
    });
  }, [theme, showInvisible]);

  // Initialize physics engine
  useEffect(() => {
    if (!canvasRef.current || !width || !height) return;

    const engine = Matter.Engine.create({
      // Moderate optimization - not too aggressive to maintain stability
      positionIterations: 8,
      velocityIterations: 6,
    });
    const world = engine.world;

    // Enable sleeping for better performance with many bodies
    engine.enableSleeping = true;

    const render = Matter.Render.create({
      canvas: canvasRef.current,
      engine: engine,
      options: {
        width: width,
        height: height,
        wireframes: false,
        background: canvasColor,
        // Performance optimizations - disable all debug rendering
        showDebug: false,
        showBroadphase: false,
        showBounds: false,
        showVelocity: false,
        showAngleIndicator: false,
        showSeparations: false,
        showCollisions: false,
        showVertexNumbers: false,
        showConvexHulls: false,
        showInternalEdges: false,
        // Reduce pixel ratio for better performance on high-DPI displays
        pixelRatio: Math.min(window.devicePixelRatio || 1, 2),
      },
    });

    const handleWheel = (event: WheelEvent) => {
      event.preventDefault();
      setWallOffsetY(prevOffsetY => prevOffsetY + event.deltaY);
    };

    const canvas = render.canvas;
    canvas.addEventListener('wheel', handleWheel);

    const ground = Matter.Bodies.rectangle(width / 2, height - 10, width, 20, {
      label: 'ground',
      isStatic: true,
      render: {
        fillStyle: floorColor,
        strokeStyle: wallBorderColor,
        lineWidth: 2,
      },
      // Performance optimization: Wall/floor collision category
      collisionFilter: {
        category: 0x0004, // Wall/floor category
        mask: 0xFFFFFFFF, // Collides with everything
      },
    });
    const leftWall = Matter.Bodies.rectangle(-10, height / 2, 20, height, {
      label: 'leftWall',
      isStatic: true,
      render: {
        fillStyle: floorColor,
        strokeStyle: wallBorderColor,
        lineWidth: 2,
      },
      // Performance optimization: Wall/floor collision category
      collisionFilter: {
        category: 0x0004, // Wall/floor category
        mask: 0xFFFFFFFF, // Collides with everything
      },
    });
    const rightWall = Matter.Bodies.rectangle(width + 10, height / 2, 20, height, {
      label: 'rightWall',
      isStatic: true,
      render: {
        fillStyle: floorColor,
        strokeStyle: wallBorderColor,
        lineWidth: 2,
      },
      // Performance optimization: Wall/floor collision category
      collisionFilter: {
        category: 0x0004, // Wall/floor category
        mask: 0xFFFFFFFF, // Collides with everything
      },
    });

    const mouse = Matter.Mouse.create(render.canvas);
    const mouseConstraint = Matter.MouseConstraint.create(engine, {
      mouse: mouse,
      constraint: {
        stiffness: 0.2,
        render: {
          visible: false,
        },
      },
    });
    mouseConstraintRef.current = mouseConstraint;

    // On release, if the object was held against a surface, cancel its velocity to prevent it from flying away.
    Matter.Events.on(mouseConstraint, 'enddrag', (event) => {
      const body = (event as any).body;
      const { floor, leftWall, rightWall } = heldSurfaceInfoRef.current;

      if (floor || leftWall || rightWall) {
        const newVelocity = { ...body.velocity };
        if (floor) {
          newVelocity.y = 0;
        }
        if (leftWall || rightWall) {
          newVelocity.x = 0;
        }
        Matter.Body.setVelocity(body, newVelocity);
      }
      
      // Reset after drag ends
      heldSurfaceInfoRef.current = { floor: false, leftWall: false, rightWall: false };
    });

    // Prevent objects from visually passing through walls/floor during a drag.
    Matter.Events.on(engine, 'afterUpdate', () => {
      if (mouseConstraint.body) {
        const body = mouseConstraint.body;
        const ground = Matter.Composite.allBodies(world).find(b => b.label === 'ground');
        const leftWall = Matter.Composite.allBodies(world).find(b => b.label === 'leftWall');
        const rightWall = Matter.Composite.allBodies(world).find(b => b.label === 'rightWall');

        const heldInfo = { floor: false, leftWall: false, rightWall: false };

        // Floor collision
        if (ground) {
          const groundTop = ground.position.y - 10;
          const lowestPointY = Math.max(...body.vertices.map(v => v.y));
          if (lowestPointY > groundTop) {
            const penetration = lowestPointY - groundTop;
            Matter.Body.translate(body, { x: 0, y: -penetration });
            heldInfo.floor = true;
          }
        }

        // Left wall collision
        if (leftWall) {
          const wallRightEdge = leftWall.position.x + 10;
          const leftmostPointX = Math.min(...body.vertices.map(v => v.x));
          if (leftmostPointX < wallRightEdge) {
            const penetration = wallRightEdge - leftmostPointX;
            Matter.Body.translate(body, { x: penetration, y: 0 });
            heldInfo.leftWall = true;
          }
        }

        // Right wall collision
        if (rightWall) {
          const wallLeftEdge = rightWall.position.x - 10;
          const rightmostPointX = Math.max(...body.vertices.map(v => v.x));
          if (rightmostPointX > wallLeftEdge) {
            const penetration = rightmostPointX - wallLeftEdge;
            Matter.Body.translate(body, { x: -penetration, y: 0 });
            heldInfo.rightWall = true;
          }
        }
        
        heldSurfaceInfoRef.current = heldInfo;

      } else {
        // Reset if not dragging
        heldSurfaceInfoRef.current = { floor: false, leftWall: false, rightWall: false };
      }
    });

    Matter.Composite.add(world, [ground, leftWall, rightWall, mouseConstraint]);

    // Use the 'afterRender' event to draw text on top of the bodies
    Matter.Events.on(render, 'afterRender', () => {
      const bodies = Matter.Composite.allBodies(world);
      const ctx = render.context;
      const currentTime = performance.now();

      // Performance optimization: Frame counter for render frequency control
      frameCounter.current++;

      // Performance optimization: Reduce render frequency when enabled
      const shouldSkipRender = performanceOptimizations.reduceRenderFrequency &&
                              frameCounter.current % 3 !== 0;

      // Batch text rendering operations with performance optimizations
      const textBodies: BodyWithText[] = [];
      bodies.forEach((body) => {
        const bodyWithText = body as BodyWithText;
        if (bodyWithText.textData) {
          // Performance optimization: Freeze distant bodies
          if (performanceOptimizations.freezeDistantBodies) {
            const canvas = canvasRef.current;
            if (canvas) {
              const isOffScreen = body.position.y > canvas.height + 200 ||
                                body.position.x < -200 ||
                                body.position.x > canvas.width + 200;
              if (isOffScreen) {
                Matter.Sleeping.set(body, true);
                return; // Skip rendering off-screen bodies
              }
            }
          }

          // Performance optimization: Suspend static bodies
          if (performanceOptimizations.suspendStaticBodies) {
            const bodyId = bodyWithText.id;
            const lastMovement = lastBodyMovementCheck.current.get(bodyId);
            const currentPos = { x: body.position.x, y: body.position.y };

            if (lastMovement) {
              const distance = Math.sqrt(
                Math.pow(currentPos.x - lastMovement.x, 2) +
                Math.pow(currentPos.y - lastMovement.y, 2)
              );

              // If body hasn't moved significantly in 3 seconds, put it to sleep
              if (distance < 5 && currentTime - lastMovement.time > 3000) {
                Matter.Sleeping.set(body, true);
              } else if (distance >= 5) {
                lastBodyMovementCheck.current.set(bodyId, { ...currentPos, time: currentTime });
              }
            } else {
              lastBodyMovementCheck.current.set(bodyId, { ...currentPos, time: currentTime });
            }
          }

          textBodies.push(bodyWithText);
        }
      });

      if (textBodies.length > 0 && !shouldSkipRender) {
        // Set common text properties once
        ctx.textAlign = "center";
        ctx.textBaseline = "middle";

        textBodies.forEach((bodyWithText) => {
          const body = bodyWithText as Matter.Body;
          if (!bodyWithText.textData) return;

          ctx.save();
          ctx.translate(body.position.x, body.position.y);
          ctx.rotate(body.angle);

          const { text, options } = bodyWithText.textData;
          const { fontSize, fontFamily, isBold, isItalic, color } = options;

          // Use cached font string
          const fontKey = `${isBold}-${isItalic}-${fontSize}-${fontFamily}`;
          let fontString = fontCache.current.get(fontKey);
          if (!fontString) {
            const fontStyle = isItalic ? 'italic' : 'normal';
            const fontWeight = isBold ? 'bold' : 'normal';
            fontString = `${fontStyle} ${fontWeight} ${fontSize}px ${fontFamily}`;
            fontCache.current.set(fontKey, fontString);
          }
          ctx.font = fontString;

          const currentTheme = themeRef.current;
          const finalColor = color === 'auto'
            ? (currentTheme === 'dark' ? '#ffffff' : '#000000')
            : getDisplayColor(color, currentTheme);
          ctx.fillStyle = finalColor;

          // Optimize multi-line text rendering
          const lines = text.split('\n');
          if (lines.length === 1) {
            // Single line optimization
            ctx.fillText(text, 0, 0);
          } else {
            // Multi-line with cached metrics
            const lineHeight = fontSize * 1.2;
            const totalHeight = lines.length * lineHeight;
            const startY = -totalHeight / 2 + lineHeight / 2;

            lines.forEach((line, index) => {
              const yPos = startY + (index * lineHeight);
              ctx.fillText(line, 0, yPos);
            });
          }

          ctx.restore();
        });
      }

      // Draw tool cursor
      if ((drawingTool === 'pen' || drawingTool === 'eraser') && mousePositionRef.current) {
        const mousePos = mousePositionRef.current;
        const radius = drawingTool === 'pen' ? penSize / 2 : penSize;
        
        ctx.beginPath();
        ctx.arc(mousePos.x, mousePos.y, radius, 0, 2 * Math.PI);

        if (drawingTool === 'pen') {
          ctx.fillStyle = getDisplayColor(penColor, theme);
          ctx.globalAlpha = 0.5;
          ctx.fill();
          ctx.globalAlpha = 1.0; // Reset alpha
        } else { // eraser
          ctx.fillStyle = 'rgba(255, 0, 0, 0.2)';
          ctx.fill();
          ctx.strokeStyle = 'rgba(255, 0, 0, 0.8)';
          ctx.lineWidth = 1;
          ctx.stroke();
        }
      }
    });

    const runner = Matter.Runner.create({
      // Performance optimization: Batch physics updates
      delta: performanceOptimizations.batchPhysicsUpdates ? 16.666 * 2 : 16.666, // 30fps vs 60fps
      isFixed: performanceOptimizations.batchPhysicsUpdates,
    });
    Matter.Runner.run(runner, engine);
    Matter.Render.run(render);

    engineRef.current = engine;
    renderRef.current = render;
    runnerRef.current = runner;

    return () => {
      // Comprehensive cleanup for memory management
      canvas.removeEventListener('wheel', handleWheel);

      // Cleanup optimization removed - no animation frames to cancel

      // Clear caches
      fontCache.current.clear();

      // Stop and clean up Matter.js components
      if (renderRef.current) {
        Matter.Events.off(renderRef.current, 'afterRender');
        Matter.Render.stop(renderRef.current);
      }
      if (runnerRef.current) {
        Matter.Runner.stop(runnerRef.current);
      }
      if (engineRef.current) {
        Matter.Events.off(engineRef.current, 'beforeUpdate');
        Matter.Engine.clear(engineRef.current);
      }

      // Clear refs
      engineRef.current = null;
      renderRef.current = null;
      runnerRef.current = null;
      mouseConstraintRef.current = null;
    };
  }, [width, height]); // Re-initialize on resize

  // Save drawing to JSON
  useEffect(() => {
    if (saveDrawingTrigger === 0) return;

    const world = engineRef.current?.world;
    if (!world) return;

    const drawnLines = Matter.Composite.allBodies(world).filter(
      (body) => body.label === 'drawnLine'
    );

    const drawingData = drawnLines.map((body) => {
      const width = Matter.Vector.magnitude(Matter.Vector.sub(body.vertices[1], body.vertices[0]));
      const height = Matter.Vector.magnitude(Matter.Vector.sub(body.vertices[2], body.vertices[1]));
      return {
        position: body.position,
        angle: body.angle,
        width: width,
        height: height,
        render: {
          fillStyle: body.render.fillStyle,
        },
        isInvisible: (body as any).isInvisible || false,
        originalColor: (body as any).originalColor || body.render.fillStyle,
      };
    });

    const json = JSON.stringify({ drawing: drawingData }, null, 2);
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'drawing.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

  }, [saveDrawingTrigger]);

  // Load drawing from JSON
  useEffect(() => {
    if (!drawingToLoad) return;

    const world = engineRef.current?.world;
    if (!world) return;

    // Clear existing drawing
    const existingLines = Matter.Composite.allBodies(world).filter(
      (body) => body.label === 'drawnLine'
    );
    Matter.Composite.remove(world, existingLines);

    // Add new lines
    drawingToLoad.drawing.forEach((lineData: any) => {
      const body = Matter.Bodies.rectangle(
        lineData.position.x,
        lineData.position.y,
        lineData.width,
        lineData.height,
        {
          isStatic: true,
          angle: lineData.angle,
          render: {
            fillStyle: lineData.isInvisible
              ? (showInvisible ? 'rgba(128, 128, 128, 0.5)' : 'transparent')
              : getDisplayColor(lineData.originalColor, theme),
          },
          label: 'drawnLine',
        }
      );

      if (lineData.isInvisible) {
        (body as any).isInvisible = true;
      }
      (body as any).originalColor = lineData.originalColor;

      Matter.Composite.add(world, body);
    });

  }, [drawingToLoad]);

  // Update drawings on theme change
  useEffect(() => {
    if (!engineRef.current) return;
    const world = engineRef.current.world;
    const allBodies = Matter.Composite.allBodies(world);

    allBodies.forEach(body => {
      if (body.label === 'drawnLine' && (body as any).originalColor && !(body as any).isInvisible) {
        body.render.fillStyle = getDisplayColor((body as any).originalColor, theme);
      }
    });
  }, [theme]);

  // Toggle visibility of invisible lines
  useEffect(() => {
    if (!engineRef.current) return;
    const world = engineRef.current.world;
    const allBodies = Matter.Composite.allBodies(world);

    allBodies.forEach(body => {
      if ((body as any).isInvisible) {
        body.render.fillStyle = showInvisible ? 'rgba(128, 128, 128, 0.5)' : 'transparent';
      }
    });
  }, [showInvisible]);

  // Update physics parameters - optimized to reduce frequency
  const physicsParamsRef = useRef({ gravity, friction, restitution });
  useEffect(() => {
    const params = physicsParamsRef.current;
    const hasChanged = params.gravity !== gravity || params.friction !== friction || params.restitution !== restitution;

    if (hasChanged && engineRef.current) {
      engineRef.current.world.gravity.y = gravity;

      // Batch body property updates for better performance
      const bodies = Matter.Composite.allBodies(engineRef.current.world);
      const dynamicBodies = bodies.filter(body => !body.isStatic);

      dynamicBodies.forEach((body) => {
        body.friction = friction;
        body.restitution = restitution;
      });

      // Update cached values
      physicsParamsRef.current = { gravity, friction, restitution };
    }
  }, [gravity, friction, restitution]);

  // Pause/Play simulation
  useEffect(() => {
    if (engineRef.current) {
      engineRef.current.timing.timeScale = isPaused ? 0 : 1;
    }
  }, [isPaused]);

  // Disable mouse constraint when drawing
  useEffect(() => {
    if (mouseConstraintRef.current) {
      const isDrawingActive = drawingTool === 'pen' || drawingTool === 'eraser';
      if (mouseConstraintRef.current.mouse.element) {
        // mouseConstraintRef.current.mouse.element.style.cursor = isDrawingActive ? 'none' : 'auto';
      }
      mouseConstraintRef.current.constraint.stiffness = isDrawingActive ? 0 : 0.2;
    }
  }, [drawingTool]);

  // Sync all dynamic bodies with wall offsets
  useEffect(() => {
    if (!engineRef.current) return;

    const deltaY = wallOffsetY - prevWallOffsetYRef.current;
    const deltaX = wallOffsetX - prevWallOffsetXRef.current;

    if (deltaY === 0 && deltaX === 0) return;

    const world = engineRef.current.world;
    const allBodies = Matter.Composite.allBodies(world);
    const bodiesToMove = allBodies.filter(
      b => b.label !== 'ground' && b.label !== 'leftWall' && b.label !== 'rightWall'
    );

    bodiesToMove.forEach(body => {
      Matter.Body.translate(body, { x: deltaX, y: deltaY });
    });

    prevWallOffsetYRef.current = wallOffsetY;
    prevWallOffsetXRef.current = wallOffsetX;
  }, [wallOffsetY, wallOffsetX]);

  // Update wall/floor positions and borders for debugging
  useEffect(() => {
    if (!engineRef.current || !width || !height) return;

    const world = engineRef.current.world;
    const ground = Matter.Composite.allBodies(world).find(b => b.label === 'ground');
    const leftWall = Matter.Composite.allBodies(world).find(b => b.label === 'leftWall');
    const rightWall = Matter.Composite.allBodies(world).find(b => b.label === 'rightWall');

    if (ground) {
      Matter.Body.setPosition(ground, { x: width / 2 + wallOffsetX, y: height - 10 + wallOffsetY });
      ground.render.strokeStyle = wallBorderColor;
    }
    if (leftWall) {
      Matter.Body.setPosition(leftWall, { x: -10 + wallOffsetX, y: height / 2 + wallOffsetY });
      leftWall.render.strokeStyle = wallBorderColor;
    }
    if (rightWall) {
      Matter.Body.setPosition(rightWall, { x: width + 10 + wallOffsetX, y: height / 2 + wallOffsetY });
      rightWall.render.strokeStyle = wallBorderColor;
    }
  }, [wallOffsetX, wallOffsetY, wallBorderColor, width, height]);

  // Update canvas color
  useEffect(() => {
    if (renderRef.current && engineRef.current) {
      renderRef.current.options.background = canvasColor;
      const world = engineRef.current.world;
      const ground = Matter.Composite.allBodies(world).find(b => b.label === 'ground');
      const leftWall = Matter.Composite.allBodies(world).find(b => b.label === 'leftWall');
      const rightWall = Matter.Composite.allBodies(world).find(b => b.label === 'rightWall');
      if(ground) ground.render.fillStyle = floorColor;
      if(leftWall) leftWall.render.fillStyle = floorColor;
      if(rightWall) rightWall.render.fillStyle = floorColor;
    }
  }, [canvasColor, floorColor]);

  // Handle performance optimization changes
  useEffect(() => {
    if (!engineRef.current) return;

    const world = engineRef.current.world;
    const bodies = Matter.Composite.allBodies(world);

    // Update collision filters for existing bodies
    bodies.forEach(body => {
      const bodyWithText = body as BodyWithText;
      if (bodyWithText.textData && body.label !== 'ground' && body.label !== 'leftWall' && body.label !== 'rightWall') {
        // Update collision filter based on current optimization settings
        body.collisionFilter = {
          category: performanceOptimizations.disableCollisionDetection ? 0x0002 : 0x0001,
          mask: performanceOptimizations.disableCollisionDetection ? 0x0004 : 0xFFFFFFFF,
        };
      }
    });

    // Update runner settings for batch physics updates
    if (runnerRef.current) {
      runnerRef.current.delta = performanceOptimizations.batchPhysicsUpdates ? 16.666 * 2 : 16.666;
      runnerRef.current.isFixed = performanceOptimizations.batchPhysicsUpdates;
    }
  }, [performanceOptimizations]);

  // Render preview text
  useEffect(() => {
    if (!engineRef.current || !width || !height) return;
    const world = engineRef.current.world;

    const oldPreviewBodies = Matter.Composite.allBodies(world).filter(b => (b as BodyWithText).isPreview);
    if (oldPreviewBodies.length > 0) {
      Matter.Composite.remove(world, oldPreviewBodies);
    }

    if (textToRender) {
      const x = width / 2;
      const y = height / 4; // Position preview higher up
      const isPreview = true;

      if (isCharacterMode) {
        const bodies = createCharacterBodies(textToRender, x, y, textOptions, isPreview);
        Matter.Composite.add(world, bodies);
      } else {
        const body = createTextBody(textToRender, x, y, textOptions, isPreview);
        Matter.Composite.add(world, body);
      }
    }
  }, [textToRender, isCharacterMode, isAltMode, textOptions, width, height]);

  // Drop the rendered text
  useEffect(() => {
    if (dropTrigger === 0 || !engineRef.current) return;

    const world = engineRef.current.world;
    const previewBodies = Matter.Composite.allBodies(world).filter(b => (b as BodyWithText).isPreview);

    if (previewBodies.length > 0) {
      previewBodies.forEach(body => {
        const bodyWithText = body as BodyWithText;
        if (bodyWithText.textData) {
          body.render.fillStyle = getResolvedBackgroundColor(bodyWithText.textData.options.collisionColor, themeRef.current);
          body.render.strokeStyle = 'transparent';
          body.render.lineWidth = 0;
        }
        Matter.Body.setStatic(body, false);
        bodyWithText.isPreview = false;
        bodyWithText.presetId = dropTrigger; // Assign the preset ID
      });
      onDropComplete();
    }
  }, [dropTrigger, onDropComplete]);

  // Reset physics world
  useEffect(() => {
    if (!onReset || !engineRef.current) return;
    const world = engineRef.current.world;
    const bodies = Matter.Composite.allBodies(world);
    const dynamicBodies = bodies.filter((body) => !body.isStatic);
    Matter.Composite.remove(world, dynamicBodies);
  }, [onReset]);

  // Delete preset bodies
  useEffect(() => {
    if (deletePresetId === null || !engineRef.current) return;

    const world = engineRef.current.world;
    const bodiesToDelete = Matter.Composite.allBodies(world).filter(
      (body) => (body as BodyWithText).presetId === deletePresetId
    );

    if (bodiesToDelete.length > 0) {
      Matter.Composite.remove(world, bodiesToDelete);
    }
  }, [deletePresetId]);

  // Replace preset text
  // Freeze/unfreeze preset bodies
  useEffect(() => {
    if (!engineRef.current) return;

    const world = engineRef.current.world;
    const allBodies = Matter.Composite.allBodies(world);

    allBodies.forEach(body => {
      const bodyWithText = body as BodyWithText;
      if (bodyWithText.presetId !== undefined) {
        const shouldBeFrozen = frozenPresetIds.has(bodyWithText.presetId);
        if (body.isStatic !== shouldBeFrozen) {
          Matter.Body.setStatic(body, shouldBeFrozen);
        }
      }
    });
  }, [frozenPresetIds]);

  // Replace preset text
  useEffect(() => {
    if (!replaceTrigger || !engineRef.current) return;

    const { presetId, altFilename } = replaceTrigger;
    const world = engineRef.current.world;

    const bodiesToReplace = Matter.Composite.allBodies(world).filter(
      (body) => (body as BodyWithText).presetId === presetId
    );

    if (bodiesToReplace.length === 0) {
      onReplaceComplete();
      return;
    }

    // Calculate the center of the existing bodies
    const centerX = bodiesToReplace.reduce((sum, body) => sum + body.position.x, 0) / bodiesToReplace.length;
    const centerY = bodiesToReplace.reduce((sum, body) => sum + body.position.y, 0) / bodiesToReplace.length;

    const fetchAndReplace = async () => {
      try {
        const response = await fetch(`/presets/${altFilename}`);
        if (!response.ok) throw new Error(`Failed to fetch preset file: ${altFilename}`);
        const newText = await response.text();

        // Remove old bodies
        Matter.Composite.remove(world, bodiesToReplace);

        // Create and add new bodies
        let newBodies: Matter.Body[];
        if (isCharacterMode) {
          newBodies = createCharacterBodies(newText, centerX, centerY, textOptions);
        } else {
          newBodies = [createTextBody(newText, centerX, centerY, textOptions)];
        }

        newBodies.forEach(body => {
          (body as BodyWithText).presetId = presetId; // Re-assign the same preset ID
        });

        Matter.Composite.add(world, newBodies);

      } catch (error) {
        console.error("Failed to replace text:", error);
      } finally {
        onReplaceComplete(); // Reset the trigger
      }
    };

    fetchAndReplace();
  }, [replaceTrigger, isCharacterMode, textOptions, onReplaceComplete]);

  // Mode swap effect
  useEffect(() => {
    if (!modeSwapTrigger || !engineRef.current) return;

    const { presetId, newMode } = modeSwapTrigger;
    const world = engineRef.current.world;

    const bodiesToSwap = Matter.Composite.allBodies(world).filter(
      (body) => (body as BodyWithText).presetId === presetId
    );

    if (bodiesToSwap.length === 0) {
      onModeSwapComplete();
      return;
    }

    // Capture physics state of existing bodies
    const physicsStates = bodiesToSwap.map(body => ({
      position: { x: body.position.x, y: body.position.y },
      velocity: { x: body.velocity.x, y: body.velocity.y },
      angle: body.angle,
      angularVelocity: body.angularVelocity,
      isStatic: body.isStatic
    }));

    // Calculate the center of the existing bodies for positioning new bodies
    const centerX = bodiesToSwap.reduce((sum, body) => sum + body.position.x, 0) / bodiesToSwap.length;
    const centerY = bodiesToSwap.reduce((sum, body) => sum + body.position.y, 0) / bodiesToSwap.length;

    // Get the text from the first body (they should all have the same text)
    const firstBody = bodiesToSwap[0] as BodyWithText;
    if (!firstBody.textData) {
      onModeSwapComplete();
      return;
    }

    const { text, options } = firstBody.textData;

    // Optimized body removal and creation for better memory management
    // Remove old bodies in batch
    Matter.Composite.remove(world, bodiesToSwap);

    // Force garbage collection of removed bodies by clearing references
    bodiesToSwap.forEach(body => {
      // Clear any custom properties to help GC
      delete (body as any).textData;
      delete (body as any).presetId;
      delete (body as any).isPreview;
    });

    // Create new bodies with the new mode
    let newBodies: Matter.Body[];
    if (newMode === 'character') {
      newBodies = createCharacterBodies(text, centerX, centerY, options);
    } else {
      newBodies = [createTextBody(text, centerX, centerY, options)];
    }

    // Add bodies to world in batch
    Matter.Composite.add(world, newBodies);

    // Then apply physics state and preset ID
    newBodies.forEach((body, index) => {
      (body as BodyWithText).presetId = presetId;

      // For mode transitions, we need to handle physics state preservation differently
      if (newMode === 'character' && physicsStates.length === 1) {
        // Default -> Character: Apply the single body's state to all character bodies
        const singleBodyState = physicsStates[0];

        // Calculate offset from center for this character body
        const offsetX = body.position.x - centerX;
        const offsetY = body.position.y - centerY;

        // Apply rotation to the offset to maintain relative positioning
        const cos = Math.cos(singleBodyState.angle);
        const sin = Math.sin(singleBodyState.angle);
        const rotatedOffsetX = offsetX * cos - offsetY * sin;
        const rotatedOffsetY = offsetX * sin + offsetY * cos;

        // Set the new position based on original center + rotated offset
        Matter.Body.setPosition(body, {
          x: singleBodyState.position.x + rotatedOffsetX,
          y: singleBodyState.position.y + rotatedOffsetY
        });

        // Apply physics properties
        Matter.Body.setVelocity(body, singleBodyState.velocity);
        Matter.Body.setAngle(body, singleBodyState.angle);
        Matter.Body.setAngularVelocity(body, singleBodyState.angularVelocity);

        // Apply frozen state
        if (frozenPresetIds.has(presetId) || singleBodyState.isStatic) {
          Matter.Body.setStatic(body, true);
        }
      } else if (newMode === 'default' && physicsStates.length > 1) {
        // Character -> Default: Use average physics state from all character bodies
        const avgVelocity = {
          x: physicsStates.reduce((sum, state) => sum + state.velocity.x, 0) / physicsStates.length,
          y: physicsStates.reduce((sum, state) => sum + state.velocity.y, 0) / physicsStates.length
        };
        const avgAngularVelocity = physicsStates.reduce((sum, state) => sum + state.angularVelocity, 0) / physicsStates.length;
        const avgAngle = physicsStates.reduce((sum, state) => sum + state.angle, 0) / physicsStates.length;

        Matter.Body.setVelocity(body, avgVelocity);
        Matter.Body.setAngle(body, avgAngle);
        Matter.Body.setAngularVelocity(body, avgAngularVelocity);

        // Apply frozen state if any of the original bodies were static
        const wasStatic = physicsStates.some(state => state.isStatic) || frozenPresetIds.has(presetId);
        if (wasStatic) {
          Matter.Body.setStatic(body, true);
        }
      } else if (index < physicsStates.length) {
        // Same mode transition or 1:1 mapping: preserve individual body state
        const bodyState = physicsStates[index];
        Matter.Body.setPosition(body, bodyState.position);
        Matter.Body.setVelocity(body, bodyState.velocity);
        Matter.Body.setAngle(body, bodyState.angle);
        Matter.Body.setAngularVelocity(body, bodyState.angularVelocity);

        if (bodyState.isStatic || frozenPresetIds.has(presetId)) {
          Matter.Body.setStatic(body, true);
        }
      } else {
        // Fallback: just apply frozen state if needed
        if (frozenPresetIds.has(presetId)) {
          Matter.Body.setStatic(body, true);
        }
      }
    });
    onModeSwapComplete();
  }, [modeSwapTrigger, createCharacterBodies, createTextBody, frozenPresetIds, onModeSwapComplete]);

  return (
    <div className="w-full h-full">
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
      />
      <canvas
        ref={measureCanvasRef}
        width={1}
        height={1}
        className="hidden"
      />
      <PerformanceMonitor
        engineRef={engineRef}
        enabled={showPerformanceMonitor || process.env.NODE_ENV === 'development'}
      />
    </div>
  );
};

export default KineticCanvas;
