import Matter from 'matter-js';

export interface TextOptions {
  fontFamily: string;
  fontSize: number;
  isBold: boolean;
  isItalic: boolean;
  letterSpacing: number;
  color: string;
  collisionColor: string;
  collisionBoxSizeX?: number;
  collisionBoxSizeY?: number;
}

export interface BodyWithText extends Matter.Body {
  textData?: {
    text: string;
    options: TextOptions;
  };
  isPreview?: boolean;
  presetId?: number; // Unique ID for each preset instance
}

export interface CollisionOptions {
  color: string;
  collisionBoxSizeX?: number;
  collisionBoxSizeY?: number;
}

export interface Preset {
  name: string;
  filename: string;
  alt?: string; // Alternate file for text replacement
  isAltMode?: boolean;
  textOptions: Partial<TextOptions>;
  collisionOptions: CollisionOptions;
}

export interface ActivePreset {
  id: number;
  name: string;
  text: string;
  options: TextOptions;
  mode: 'default' | 'character';
}
