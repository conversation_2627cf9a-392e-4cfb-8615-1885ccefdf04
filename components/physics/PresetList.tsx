import React from 'react';

interface Preset {
  id: number;
  name: string;
  alt?: string;
}

interface PresetListProps {
  presets: Preset[];
  onDelete: (id: number) => void;
  onReplace: (id: number, altFilename: string) => void;
  onFreeze: (id: number) => void;
  frozenPresetIds: Set<number>;
}

const PresetList: React.FC<PresetListProps> = ({ presets, onDelete, onReplace, onFreeze, frozenPresetIds }) => {
  return (
    <div className="w-64 bg-white rounded-lg shadow-lg p-4 self-start">
      <h3 className="text-xl font-semibold mb-4 border-b pb-2">Active Presets</h3>
      {presets.length === 0 ? (
        <p className="text-gray-500 text-sm">No presets have been dropped yet.</p>
      ) : (
        <ul className="space-y-2">
          {presets.map((preset) => (
            <li key={preset.id} className="flex items-center justify-between bg-gray-50 p-2 rounded-md">
              <span className="text-sm font-medium truncate" title={preset.name}>{preset.name}</span>
              <div className="flex items-center space-x-2">
                {preset.alt && (
                  <button
                    onClick={() => onReplace(preset.id, preset.alt!)}
                    className="text-blue-500 hover:text-blue-700 font-bold py-1 px-2 rounded"
                    title="Replace Text"
                  >
                    R
                  </button>
                )}
                <button
                  onClick={() => onFreeze(preset.id)}
                  className={`font-bold py-1 px-2 rounded ${
                    frozenPresetIds.has(preset.id)
                      ? 'text-cyan-500 hover:text-cyan-700'
                      : 'text-gray-400 hover:text-gray-600'
                  }`}
                  title={frozenPresetIds.has(preset.id) ? 'Unfreeze' : 'Freeze'}
                >
                  F
                </button>
                <button
                  onClick={() => onDelete(preset.id)}
                  className="text-red-500 hover:text-red-700 font-bold py-1 px-2 rounded"
                  title="Delete Preset"
                >
                  X
                </button>
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default PresetList;
