"use client"

import { useMemo } from "react"

import { useEffect, useRef, useState, useCallback } from "react"
import { motion, AnimatePresence, LayoutGroup } from "framer-motion"
import { Check, X, ArrowRight, LayoutGrid, Table } from "lucide-react"
import { cn } from "@/lib/utils"

interface PricingFeature {
  name: string
  included: boolean
  value?: string | number
  description?: string
}

interface PricingTier {
  id: string
  name: string
  price: string | number
  period?: string
  description: string
  features: PricingFeature[]
  popular?: boolean
  buttonText?: string
  buttonVariant?: "default" | "primary"
  badge?: {
    text: string
    icon?: string
    color?: string
  }
}

interface PricingComponentProps {
  className?: string
  tiers?: PricingTier[]
  currency?: string
  showToggle?: boolean
}

// Default pricing data - can be overridden via props
const defaultPricingTiers: PricingTier[] = [
  {
    id: "free",
    name: "Individuals",
    price: "Free",
    description: "For your hobby projects",
    features: [
      { name: "Email alerts", included: true, value: "Free email alerts" },
      { name: "Check frequency", included: true, value: "3-minute checks" },
      { name: "Data enrichment", included: true, value: "Automatic data enrichment" },
      { name: "Monitors", included: true, value: "10 monitors" },
      { name: "Team seats", included: true, value: "Up to 3 seats" },
      { name: "Phone calls", included: false },
      { name: "User accounts", included: false },
    ],
    buttonText: "Get started",
    buttonVariant: "default",
  },
  {
    id: "teams",
    name: "Teams",
    price: 90,
    period: "month/user",
    description: "Great for small businesses",
    features: [
      { name: "Email alerts", included: true, value: "Premium email alerts" },
      { name: "Check frequency", included: true, value: "30 second checks" },
      { name: "Data enrichment", included: true, value: "Advanced data enrichment" },
      { name: "Monitors", included: true, value: "20 monitors" },
      { name: "Team seats", included: true, value: "Up to 6 seats" },
      { name: "Phone calls", included: true, value: "Unlimited phone calls" },
      { name: "User accounts", included: true, value: "Single-user account" },
    ],
    popular: true,
    buttonText: "Get started",
    buttonVariant: "primary",
    badge: {
      text: "Most Popular",
      icon: "🔥",
      color: "orange",
    },
  },
  {
    id: "organizations",
    name: "Organizations",
    price: 120,
    period: "month/user",
    description: "Great for large businesses",
    features: [
      { name: "Email alerts", included: true, value: "Enterprise email alerts" },
      { name: "Check frequency", included: true, value: "15 second checks" },
      { name: "Data enrichment", included: true, value: "Enterprise data enrichment" },
      { name: "Monitors", included: true, value: "50 monitors" },
      { name: "Team seats", included: true, value: "Up to 10 seats" },
      { name: "Phone calls", included: true, value: "Unlimited phone calls" },
      { name: "User accounts", included: true, value: "Multi-user accounts" },
    ],
    buttonText: "Get started",
    buttonVariant: "default",
  },
]

// Animation state enum for better state management
type AnimationState = "idle" | "transitioning" | "complete"

export function PricingComponent({
  className,
  tiers = defaultPricingTiers,
  currency = "USD",
  showToggle = true,
}: PricingComponentProps) {
  // Simplified state management
  const [viewMode, setViewMode] = useState<"cards" | "table">("cards")
  const [animationState, setAnimationState] = useState<AnimationState>("idle")
  const [isReady, setIsReady] = useState(false)
  const [hasInteracted, setHasInteracted] = useState(false)
  const [hoveredButton, setHoveredButton] = useState<string | null>(null)
  const [clickedButton, setClickedButton] = useState<string | null>(null)

  // Single ref for cleanup management
  const cleanupRef = useRef<(() => void) | null>(null)
  const componentMountedRef = useRef(false)

  // Extract all unique feature names from all tiers
  const allFeatureNames = useMemo(() => {
    const featureSet = new Set<string>()
    tiers.forEach((tier) => {
      tier.features.forEach((feature) => {
        featureSet.add(feature.name)
      })
    })
    return Array.from(featureSet)
  }, [tiers])

  // Find the maximum number of features across all tiers for consistent card heights
  const maxFeatures = useMemo(() => {
    return Math.max(...tiers.map((tier) => tier.features.length))
  }, [tiers])

  // Unified animation configuration for consistency
  const animationConfig = {
    type: "spring" as const,
    damping: 25,
    stiffness: 400,
    mass: 0.8,
  }

  // Layout transition config - optimized for morphing
  const layoutConfig = {
    ...animationConfig,
    damping: 28,
    stiffness: 450,
  }

  // Text transition config - specifically for title and price elements
  const textTransitionConfig = {
    ...animationConfig,
    damping: 22,
    stiffness: 380,
  }

  // Stagger config for sequential animations
  const staggerConfig = {
    ...animationConfig,
    damping: 22,
    stiffness: 380,
  }

  // Enhanced button animation configurations
  const buttonAnimations = {
    // Toggle button animations
    toggle: {
      initial: { scale: 1, y: 0 },
      hover: {
        scale: 1.05,
        y: -2,
        transition: {
          type: "spring",
          stiffness: 400,
          damping: 25,
          mass: 0.5,
        },
      },
      tap: {
        scale: 0.95,
        y: 0,
        transition: {
          type: "spring",
          stiffness: 600,
          damping: 30,
          mass: 0.3,
        },
      },
      disabled: {
        scale: 1,
        opacity: 0.6,
        transition: { duration: 0.2 },
      },
    },
    // CTA button animations
    cta: {
      initial: { scale: 1, y: 0 },
      hover: {
        scale: 1.03,
        y: -3,
        transition: {
          type: "spring",
          stiffness: 500,
          damping: 25,
          mass: 0.4,
        },
      },
      tap: {
        scale: 0.97,
        y: 0,
        transition: {
          type: "spring",
          stiffness: 700,
          damping: 30,
          mass: 0.2,
        },
      },
      focus: {
        scale: 1.02,
        boxShadow: "0 0 0 3px rgba(0, 0, 0, 0.1)",
        transition: { duration: 0.2 },
      },
    },
  }

  // Enhanced button variants with improved animations
  const buttonVariants = {
    // Background gradient animation
    backgroundGradient: {
      initial: { backgroundPosition: "0% 50%" },
      hover: {
        backgroundPosition: "100% 50%",
        transition: {
          duration: 0.6,
          ease: "easeInOut",
        },
      },
    },
    // Shimmer effect
    shimmer: {
      initial: { x: "-100%" },
      hover: {
        x: "100%",
        transition: {
          duration: 0.8,
          ease: "easeInOut",
          repeat: Number.POSITIVE_INFINITY,
          repeatDelay: 2,
        },
      },
    },
    // Ripple effect
    ripple: {
      initial: { scale: 0, opacity: 0.6 },
      animate: {
        scale: 4,
        opacity: 0,
        transition: {
          duration: 0.6,
          ease: "easeOut",
        },
      },
    },
    // Arrow animation
    arrow: {
      initial: { x: 0, opacity: 1 },
      hover: {
        x: 4,
        transition: {
          type: "spring",
          stiffness: 400,
          damping: 25,
        },
      },
      tap: {
        x: 8,
        transition: {
          type: "spring",
          stiffness: 600,
          damping: 30,
        },
      },
    },
    // Text animation
    text: {
      initial: { x: 0 },
      hover: {
        x: -2,
        transition: {
          type: "spring",
          stiffness: 400,
          damping: 25,
        },
      },
    },
  }

  // Optimized view toggle with proper state management
  const toggleView = useCallback(() => {
    if (animationState !== "idle" || !isReady) return

    setAnimationState("transitioning")
    setHasInteracted(true)

    // Clear any existing cleanup
    if (cleanupRef.current) {
      cleanupRef.current()
    }

    // Toggle view mode
    setViewMode((prev) => (prev === "cards" ? "table" : "cards"))

    // Set up cleanup for animation completion
    const animationTimer = setTimeout(() => {
      if (componentMountedRef.current) {
        setAnimationState("complete")

        // Reset to idle after a brief delay
        const resetTimer = setTimeout(() => {
          if (componentMountedRef.current) {
            setAnimationState("idle")
          }
        }, 100)

        cleanupRef.current = () => clearTimeout(resetTimer)
      }
    }, 900) // Slightly longer to ensure all text transitions complete

    cleanupRef.current = () => clearTimeout(animationTimer)
  }, [animationState, isReady])

  // Get feature value for a specific tier and feature name
  const getFeatureValue = useCallback((tier: PricingTier, featureName: string): string | boolean => {
    const feature = tier.features.find((f) => f.name === featureName)
    if (!feature) return false

    if (!feature.included) return false
    if (feature.value !== undefined) return feature.value.toString()
    return true
  }, [])

  // Format price display
  const formatPrice = useCallback((price: string | number, currency: string): string => {
    if (typeof price === "string") return price
    return `${currency} ${price}`
  }, [])

  // Enhanced button styling with animation support
  const getButtonStyles = useCallback((variant = "default", popular = false, isHovered = false) => {
    const baseStyles = "relative overflow-hidden transition-all duration-300 ease-out"

    if (variant === "primary" || popular) {
      return cn(
        baseStyles,
        "bg-neutral-800 text-white",
        "hover:bg-neutral-700"
      )
    }
    return cn(
      baseStyles,
      "bg-gray-100 text-neutral-800",
      "hover:bg-gray-200",
      "border border-gray-300"
    )
  }, [])

  // Get badge styling based on color
  const getBadgeStyles = useCallback((color = "orange") => {
    const colorMap = {
      orange: "bg-gray-100 text-neutral-700 px-2 py-0.5 rounded-md font-medium", // For 'Most Popular'
      blue: "text-blue-600",
      green: "text-green-600",
      purple: "text-purple-600",
      red: "text-red-600",
    }
    // For other potential badges, we can default to a simple text color or a similar subtle background style.
    // For now, only 'orange' (Most Popular) gets the background.
    if (color === 'orange') {
      return colorMap.orange;
    }
    return colorMap[color as keyof typeof colorMap] || `text-${color}-600`; // Fallback for other colors
  }, [])

  // Handle button click with animation feedback
  const handleButtonClick = useCallback((tierId: string) => {
    setClickedButton(tierId)

    // Create ripple effect
    setTimeout(() => {
      setClickedButton(null)
    }, 600)

    // Add your actual click handler logic here
    console.log(`Button clicked for tier: ${tierId}`)
  }, [])

  // Animation variants for consistent behavior
  const containerVariants = {
    cards: {
      transition: layoutConfig,
    },
    table: {
      transition: layoutConfig,
    },
  }

  // Text animation variants for consistent title and price transitions
  const textVariants = {
    cards: {
      scale: 1,
      opacity: 1,
      transition: textTransitionConfig,
    },
    table: {
      scale: 1,
      opacity: 1,
      transition: textTransitionConfig,
    },
  }

  // Price display variants for smooth transitions
  const priceVariants = {
    cards: {
      scale: 1,
      opacity: 1,
      transition: textTransitionConfig,
    },
    table: {
      scale: 1,
      opacity: 1,
      transition: textTransitionConfig,
    },
  }

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      transition: animationConfig,
    },
    visible: (index: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        ...staggerConfig,
        delay: index * 0.05,
      },
    }),
  }

  // Initialize component
  useEffect(() => {
    componentMountedRef.current = true

    // Set ready state after initial render
    const readyTimer = setTimeout(() => {
      if (componentMountedRef.current) {
        setIsReady(true)
      }
    }, 150) // Slightly longer delay to ensure layout is established

    return () => {
      componentMountedRef.current = false
      clearTimeout(readyTimer)
      if (cleanupRef.current) {
        cleanupRef.current()
      }
    }
  }, [])

  // Auto-trigger view toggle sequence to initialize animations
  useEffect(() => {
    if (!isReady || hasInteracted) return

    // Wait a bit for the component to fully render
    const initTimer = setTimeout(() => {
      if (!componentMountedRef.current) return

      // First toggle to table view
      toggleView()

      // Wait for animation to complete, then toggle back to cards
      const returnTimer = setTimeout(() => {
        if (componentMountedRef.current && animationState === "idle") {
          toggleView()
        }
      }, 1200) // Wait for the full animation cycle to complete

      cleanupRef.current = () => {
        clearTimeout(returnTimer)
      }
    }, 500) // Small delay to ensure component is fully ready

    return () => {
      clearTimeout(initTimer)
    }
  }, [isReady, hasInteracted, animationState, toggleView])

  return (
    <div className={cn("w-full max-w-7xl mx-auto px-4", className)}>
      {/* Header Section */}
      <motion.div
        className="text-center mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={animationConfig}
      >
        {showToggle && (
          <div className="flex justify-center mb-4">
            <motion.button
              onClick={toggleView}
              disabled={animationState !== "idle"}
              className={cn(
                "flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium",
                "bg-white text-gray-700 border border-gray-300 rounded-md",
                "hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black",
                animationState !== "idle" && "opacity-60 cursor-not-allowed",
              )}
              variants={buttonAnimations.toggle}
              initial="initial"
              whileHover={animationState === "idle" ? "hover" : "disabled"}
              whileTap={animationState === "idle" ? "tap" : undefined}
              whileFocus="focus"
              style={{ willChange: "transform" }}
            >
              <motion.div
                variants={buttonVariants.arrow}
                initial="initial"
                whileHover="hover"
                className="flex items-center"
              >
                {viewMode === "cards" ? <Table className="w-4 h-4" /> : <LayoutGrid className="w-4 h-4" />}
              </motion.div>

              <motion.span variants={buttonVariants.text} initial="initial" whileHover="hover">
                {viewMode === "cards" ? "View as table" : "View as cards"}
              </motion.span>

              <motion.div variants={buttonVariants.arrow} initial="initial" whileHover="hover">
                <ArrowRight className="w-4 h-4" />
              </motion.div>
            </motion.button>
          </div>
        )}
      </motion.div>

      {/* Main Pricing Container */}
      <LayoutGroup id="pricing-morphing">
        <motion.div
          layout
          layoutId="pricing-main-container"
          variants={containerVariants}
          animate={viewMode}
          className="relative"
          style={{
            willChange: "transform",
            transformOrigin: "center center",
          }}
        >
          {/* Morphing Background Container */}
          <motion.div
            layout
            layoutId="pricing-background"
            transition={layoutConfig}
            className={cn(
              "relative overflow-hidden",
              viewMode === "table" ? "bg-white border border-gray-200 rounded-lg" : "bg-transparent",
            )}
            style={{
              willChange: "transform, background-color, border-radius",
              transformOrigin: "center center",
            }}
          >
            {/* Table Header - Conditional Rendering with proper text transitions */}
            <AnimatePresence mode="wait">
              {viewMode === "table" && (
                <motion.div
                  key="table-header"
                  layout
                  layoutId="table-header"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={animationConfig}
                  className="bg-gray-50 border-b border-gray-200"
                  style={{ willChange: "opacity, height" }}
                >
                  <div className={`grid grid-cols-${tiers.length + 1} p-6`}>
                    <div className="font-semibold text-gray-900">Features</div>
                    {tiers.map((tier, index) => (
                      <div key={`header-${tier.id}`} className="text-center">
                        <motion.h3
                          layoutId={`tier-title-${tier.id}`}
                          variants={textVariants}
                          initial={!hasInteracted ? "cards" : false}
                          animate={viewMode}
                          transition={{
                            ...textTransitionConfig,
                            delay: hasInteracted ? 0.1 + index * 0.05 : 0,
                          }}
                          className="text-lg font-semibold text-gray-900"
                          style={{ willChange: "transform, opacity" }}
                        >
                          {tier.name}
                        </motion.h3>
                      </div>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Content Container - Morphs between layouts */}
            <motion.div
              layout
              layoutId="pricing-content"
              transition={layoutConfig}
              className={cn(
                "relative",
                viewMode === "cards"
                  ? `grid gap-8 items-stretch ${
                      tiers.length === 1
                        ? "grid-cols-1 max-w-md mx-auto"
                        : tiers.length === 2
                          ? "grid-cols-1 md:grid-cols-2"
                          : tiers.length === 3
                            ? "grid-cols-1 md:grid-cols-3"
                            : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
                    }`
                  : "divide-y divide-gray-200",
              )}
              style={{
                willChange: "transform",
                transformOrigin: "center center",
              }}
            >
              {viewMode === "cards" ? (
                // Cards Layout with Enhanced Button Animations
                tiers.map((tier, index) => (
                  <motion.div
                    key={`card-${tier.id}`}
                    layoutId={`tier-card-${tier.id}`}
                    custom={index}
                    variants={itemVariants}
                    initial={!isReady ? "hidden" : false}
                    animate="visible"
                    transition={layoutConfig}
                    className={cn(
                      "relative bg-white border rounded-lg overflow-hidden transition-colors duration-200 flex flex-col",
                      // Dynamic height based on content
                      "min-h-[580px]",
                      tier.popular ? "border-neutral-700" : "border-gray-200",
                    )}
                    style={{
                      willChange: "transform",
                      transformOrigin: "center center",
                    }}
                  >
                    {/* Badge Container - Fixed Height */}
                    <motion.div
                      className="h-8 flex justify-end items-start p-4 pb-0"
                      style={{ willChange: "transform" }}
                    >
                      {tier.badge && (
                        <motion.div
                          layoutId={`popular-badge-${tier.id}`}
                          transition={layoutConfig}
                          style={{ willChange: "transform" }}
                        >
                          <span
                            className={cn("flex items-center text-sm font-medium", getBadgeStyles(tier.badge.color))}
                          >
                            {tier.badge.icon && <span className="mr-1">{tier.badge.icon}</span>}
                            {tier.badge.text}
                          </span>
                        </motion.div>
                      )}
                    </motion.div>

                    {/* Card Header - Fixed Height Container */}
                    <motion.div
                      layoutId={`tier-header-${tier.id}`}
                      transition={layoutConfig}
                      className="px-6 pb-4"
                      style={{ willChange: "transform" }}
                    >
                      <motion.h3
                        layoutId={`tier-title-${tier.id}`}
                        variants={textVariants}
                        initial="cards"
                        animate={viewMode}
                        transition={{
                          ...textTransitionConfig,
                          delay: hasInteracted ? 0.1 + index * 0.05 : 0,
                        }}
                        className="text-xl font-medium text-gray-900 mb-4 h-7 flex items-center"
                        style={{ willChange: "transform, opacity" }}
                      >
                        {tier.name}
                      </motion.h3>

                      {/* Price Container - Fixed Height */}
                      <motion.div
                        layoutId={`price-display-${tier.id}`}
                        variants={priceVariants}
                        initial="cards"
                        animate={viewMode}
                        transition={{
                          ...textTransitionConfig,
                          delay: hasInteracted ? 0.15 + index * 0.05 : 0,
                        }}
                        className="h-20 flex flex-col justify-center"
                        style={{ willChange: "transform, opacity" }}
                      >
                        <div className="flex items-baseline">
                          <motion.span
                            layoutId={`price-amount-${tier.id}`}
                            variants={textVariants}
                            initial="cards"
                            animate={viewMode}
                            transition={{
                              ...textTransitionConfig,
                              delay: hasInteracted ? 0.2 + index * 0.05 : 0,
                            }}
                            className="text-3xl font-bold text-gray-900"
                            style={{ willChange: "transform, opacity" }}
                          >
                            {formatPrice(tier.price, currency)}
                          </motion.span>
                        </div>
                        {tier.period && (
                          <motion.span
                            layoutId={`price-period-${tier.id}`}
                            variants={textVariants}
                            initial="cards"
                            animate={viewMode}
                            transition={{
                              ...textTransitionConfig,
                              delay: hasInteracted ? 0.25 + index * 0.05 : 0,
                            }}
                            className="text-gray-500 text-sm mt-1"
                            style={{ willChange: "transform, opacity" }}
                          >
                            Per {tier.period}
                          </motion.span>
                        )}
                      </motion.div>

                      {/* Description Container - Fixed Height */}
                      <motion.div className="h-12 flex items-start">
                        <motion.p
                          layoutId={`tier-desc-${tier.id}`}
                          transition={layoutConfig}
                          className="text-gray-600 text-sm leading-relaxed"
                        >
                          {tier.description}
                        </motion.p>
                      </motion.div>
                    </motion.div>

                    {/* Features Container - Flexible Height with Consistent Spacing */}
                    <motion.div
                      layoutId={`features-container-${tier.id}`}
                      transition={layoutConfig}
                      className="flex-1 px-6 pb-6 flex flex-col"
                      style={{ willChange: "transform" }}
                    >
                      <motion.ul
                        layoutId={`features-${tier.id}`}
                        transition={layoutConfig}
                        className="space-y-3 flex-1"
                        style={{ willChange: "transform" }}
                      >
                        {/* Render features dynamically with consistent spacing */}
                        {Array.from({ length: maxFeatures }).map((_, featureIndex) => {
                          const feature = tier.features[featureIndex]
                          return (
                            <motion.li
                              key={`feature-${tier.id}-${featureIndex}`}
                              layoutId={`feature-item-${tier.id}-${featureIndex}`}
                              transition={{
                                ...staggerConfig,
                                delay: featureIndex * 0.02,
                              }}
                              className="flex items-center gap-2 h-6"
                              style={{ willChange: "transform" }}
                            >
                              {feature ? (
                                <>
                                  {feature.included ? (
                                    <Check className="w-5 h-5 text-green-600" />
                                  ) : (
                                    <X className="w-5 h-5 text-gray-300 flex-shrink-0" />
                                  )}
                                  <span className={cn("text-sm", feature.included ? "text-gray-600" : "text-gray-400")}>
                                    {feature.value || feature.name}
                                  </span>
                                </>
                              ) : (
                                // Empty space to maintain consistent height
                                <div className="h-6"></div>
                              )}
                            </motion.li>
                          )
                        })}
                      </motion.ul>

                      {/* Enhanced Button Container - Fixed Position at Bottom */}
                      <motion.div className="mt-6">
                        <motion.button
                          layoutId={`cta-${tier.id}`}
                          transition={layoutConfig}
                          variants={buttonAnimations.cta}
                          initial="initial"
                          whileHover="hover"
                          whileTap="tap"
                          whileFocus="focus"
                          onHoverStart={() => setHoveredButton(tier.id)}
                          onHoverEnd={() => setHoveredButton(null)}
                          onClick={() => handleButtonClick(tier.id)}
                          className={cn(
                            "w-full py-3 rounded-md font-medium flex items-center justify-center gap-2",
                            getButtonStyles(tier.buttonVariant, tier.popular, hoveredButton === tier.id),
                          )}
                          style={{ willChange: "transform" }}
                        >
                          {/* Background gradient animation */}
                          <motion.div
                            className="absolute inset-0 rounded-md"
                            variants={buttonVariants.backgroundGradient}
                            initial="initial"
                            whileHover="hover"
                            style={{
                              background: tier.popular
                                ? "linear-gradient(90deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 100%)"
                                : "linear-gradient(90deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0.05) 100%)",
                              backgroundSize: "200% 100%",
                            }}
                          />

                          {/* Shimmer effect */}
                          <motion.div
                            className="absolute inset-0 rounded-md overflow-hidden"
                            initial="initial"
                            whileHover="hover"
                          >
                            <motion.div
                              variants={buttonVariants.shimmer}
                              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent w-full h-full"
                              style={{ willChange: "transform" }}
                            />
                          </motion.div>

                          {/* Ripple effect on click */}
                          <AnimatePresence>
                            {clickedButton === tier.id && (
                              <motion.div
                                className="absolute inset-0 rounded-md bg-white/30"
                                variants={buttonVariants.ripple}
                                initial="initial"
                                animate="animate"
                                exit="initial"
                                style={{ willChange: "transform, opacity" }}
                              />
                            )}
                          </AnimatePresence>

                          {/* Button content */}
                          <motion.span
                            variants={buttonVariants.text}
                            initial="initial"
                            whileHover="hover"
                            className="relative z-10"
                          >
                            {tier.buttonText || "Get started"}
                          </motion.span>

                          <motion.div
                            variants={buttonVariants.arrow}
                            initial="initial"
                            whileHover="hover"
                            whileTap="tap"
                            className="relative z-10"
                          >
                            <ArrowRight className="w-4 h-4" />
                          </motion.div>
                        </motion.button>
                      </motion.div>
                    </motion.div>
                  </motion.div>
                ))
              ) : (
                // Table Layout with Enhanced Button Animations
                <>
                  {/* Pricing Row with synchronized text transitions */}
                  <motion.div
                    layout
                    layoutId="pricing-row"
                    transition={layoutConfig}
                    className={`grid grid-cols-${tiers.length + 1} bg-gray-50 border-b border-gray-200`}
                    style={{ willChange: "transform" }}
                  >
                    <div className="p-4 font-medium text-gray-900">Pricing</div>
                    {tiers.map((tier, index) => (
                      <motion.div
                        key={`price-cell-${tier.id}`}
                        layoutId={`price-display-${tier.id}`}
                        variants={priceVariants}
                        initial={!hasInteracted ? "table" : false}
                        animate={viewMode}
                        transition={{
                          ...textTransitionConfig,
                          delay: hasInteracted ? 0.15 + index * 0.05 : 0,
                        }}
                        className="p-4 text-center relative"
                        style={{ willChange: "transform, opacity" }}
                      >
                        {tier.badge && (
                          <motion.div
                            layoutId={`popular-badge-${tier.id}`}
                            transition={layoutConfig}
                            className="absolute top-0 left-0 right-0 bg-orange-100 text-orange-500 text-xs py-1 text-center font-medium"
                            style={{ willChange: "transform" }}
                          >
                            {tier.badge.icon && <span className="mr-1">{tier.badge.icon}</span>}
                            {tier.badge.text}
                          </motion.div>
                        )}
                        <div className={tier.badge ? "mt-6" : ""}>
                          <motion.span
                            layoutId={`price-amount-${tier.id}`}
                            variants={textVariants}
                            initial={!hasInteracted ? "table" : false}
                            animate={viewMode}
                            transition={{
                              ...textTransitionConfig,
                              delay: hasInteracted ? 0.2 + index * 0.05 : 0,
                            }}
                            className="text-xl font-bold text-gray-900"
                            style={{ willChange: "transform, opacity" }}
                          >
                            {formatPrice(tier.price, currency)}
                          </motion.span>
                          {tier.period && (
                            <motion.div
                              layoutId={`price-period-${tier.id}`}
                              variants={textVariants}
                              initial={!hasInteracted ? "table" : false}
                              animate={viewMode}
                              transition={{
                                ...textTransitionConfig,
                                delay: hasInteracted ? 0.25 + index * 0.05 : 0,
                              }}
                              className="text-gray-500 text-sm"
                              style={{ willChange: "transform, opacity" }}
                            >
                              Per {tier.period}
                            </motion.div>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </motion.div>

                  {/* Feature Rows - Dynamic */}
                  {allFeatureNames.map((featureName, featureIndex) => (
                    <motion.div
                      key={`feature-row-${featureName}`}
                      layout
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{
                        ...animationConfig,
                        delay: 0.3 + featureIndex * 0.03,
                      }}
                      className={`grid grid-cols-${tiers.length + 1} hover:bg-gray-50 transition-colors duration-150`}
                      style={{ willChange: "opacity" }}
                    >
                      <div className="p-4 font-medium text-gray-900">{featureName}</div>
                      {tiers.map((tier) => {
                        const value = getFeatureValue(tier, featureName)
                        return (
                          <motion.div
                            key={`feature-${tier.id}-${featureName}`}
                            className="p-4 text-center"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{
                              ...animationConfig,
                              delay: 0.3 + featureIndex * 0.03,
                            }}
                            style={{ willChange: "opacity" }}
                          >
                            {typeof value === "boolean" ? (
                              value ? (
                                <div className="flex justify-center">
                                  <Check className="w-5 h-5 text-green-500" />
                                </div>
                              ) : (
                                <div className="flex justify-center">
                                  <X className="w-5 h-5 text-gray-300" />
                                </div>
                              )
                            ) : (
                              <span className="text-gray-600">{value}</span>
                            )}
                          </motion.div>
                        )
                      })}
                    </motion.div>
                  ))}

                  {/* Enhanced CTA Row - Dynamic */}
                  <motion.div
                    layout
                    layoutId="cta-row"
                    transition={layoutConfig}
                    className={`grid grid-cols-${tiers.length + 1} bg-gray-50 border-t border-gray-200`}
                    style={{ willChange: "transform" }}
                  >
                    <div className="p-6"></div>
                    {tiers.map((tier) => (
                      <div key={`cta-cell-${tier.id}`} className="p-6">
                        <motion.button
                          layoutId={`cta-${tier.id}`}
                          transition={layoutConfig}
                          variants={buttonAnimations.cta}
                          initial="initial"
                          whileHover="hover"
                          whileTap="tap"
                          whileFocus="focus"
                          onHoverStart={() => setHoveredButton(`table-${tier.id}`)}
                          onHoverEnd={() => setHoveredButton(null)}
                          onClick={() => handleButtonClick(tier.id)}
                          className={cn(
                            "w-full py-2 rounded-md font-medium transition-all duration-200 relative overflow-hidden",
                            getButtonStyles(tier.buttonVariant, tier.popular, hoveredButton === `table-${tier.id}`),
                          )}
                          style={{ willChange: "transform" }}
                        >
                          {/* Background gradient animation */}
                          <motion.div
                            className="absolute inset-0 rounded-md"
                            variants={buttonVariants.backgroundGradient}
                            initial="initial"
                            whileHover="hover"
                            style={{
                              background: tier.popular
                                ? "linear-gradient(90deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 100%)"
                                : "linear-gradient(90deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0.05) 100%)",
                              backgroundSize: "200% 100%",
                            }}
                          />

                          {/* Ripple effect on click */}
                          <AnimatePresence>
                            {clickedButton === tier.id && (
                              <motion.div
                                className="absolute inset-0 rounded-md bg-white/30"
                                variants={buttonVariants.ripple}
                                initial="initial"
                                animate="animate"
                                exit="initial"
                                style={{ willChange: "transform, opacity" }}
                              />
                            )}
                          </AnimatePresence>

                          <span className="relative z-10">{tier.buttonText || "Get started"}</span>
                        </motion.button>
                      </div>
                    ))}
                  </motion.div>
                </>
              )}
            </motion.div>
          </motion.div>
        </motion.div>
      </LayoutGroup>
    </div>
  )
}
